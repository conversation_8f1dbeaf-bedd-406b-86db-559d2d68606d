<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Creation Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group { 
            margin-bottom: 15px; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold;
        }
        input, select, textarea { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button { 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .alert { 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 4px; 
            font-weight: bold;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb;
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px; 
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 12px; 
            text-align: left; 
        }
        th { 
            background-color: #f8f9fa; 
            font-weight: bold;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Invoice Creation Test</h1>
        
        <div id="alertContainer"></div>
        
        <div class="test-section">
            <h3>System Status</h3>
            <button onclick="checkSystemStatus()">Check System Status</button>
            <button onclick="initializeSystem()">Initialize System</button>
            <button onclick="testInvoiceCreation()">Test Invoice Creation</button>
            <div id="statusContainer"></div>
        </div>

        <div class="test-section">
            <h3>Create Invoice</h3>
            <form id="createInvoiceForm">
                <div class="form-group">
                    <label for="customerId">Customer</label>
                    <select id="customerId" name="customerId" required>
                        <option value="">Select Customer</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="amount">Amount ($)</label>
                    <input type="number" id="amount" name="amount" step="0.01" required placeholder="0.00">
                </div>
                <div class="form-group">
                    <label for="invoiceDate">Invoice Date</label>
                    <input type="date" id="invoiceDate" name="invoiceDate" required>
                </div>
                <div class="form-group">
                    <label for="dueDate">Due Date</label>
                    <input type="date" id="dueDate" name="dueDate" required>
                </div>
                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea id="notes" name="notes" rows="3" placeholder="Optional notes..."></textarea>
                </div>
                <button type="submit">Create Invoice</button>
                <button type="button" onclick="resetForm()">Reset Form</button>
            </form>
        </div>
        
        <div class="test-section">
            <h3>Invoices</h3>
            <button onclick="refreshInvoices()">Refresh Invoices</button>
            <table>
                <thead>
                    <tr>
                        <th>Invoice #</th>
                        <th>Customer</th>
                        <th>Amount</th>
                        <th>Date</th>
                        <th>Due Date</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="invoicesTableBody">
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <h3>Customers</h3>
            <button onclick="refreshCustomers()">Refresh Customers</button>
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Address</th>
                    </tr>
                </thead>
                <tbody id="customersTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <script src="JS/Invoices.js"></script>
    <script>
        // Override alert functions for testing
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${type}`;
            alertDiv.textContent = message;
            alertContainer.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
            
            console.log(`${type.toUpperCase()}: ${message}`);
        }

        // Override invoicesManager alert function
        if (window.invoicesManager) {
            window.invoicesManager.showAlert = showAlert;
            window.invoicesManager.hideModal = function(modalId) {
                console.log(`Modal ${modalId} would be hidden`);
            };
        }

        function checkSystemStatus() {
            const statusContainer = document.getElementById('statusContainer');
            let status = '<div class="alert info"><h4>System Status:</h4>';
            
            status += `<p>✅ InvoicesManager exists: ${!!window.invoicesManager}</p>`;
            
            if (window.invoicesManager) {
                status += `<p>✅ Initialized: ${window.invoicesManager.isInitialized}</p>`;
                status += `<p>📊 Customers: ${window.invoicesManager.customers.length}</p>`;
                status += `<p>📄 Invoices: ${window.invoicesManager.invoices.length}</p>`;
            }
            
            status += '</div>';
            statusContainer.innerHTML = status;
        }

        function initializeSystem() {
            if (window.invoicesManager) {
                window.invoicesManager.init();
                showAlert('System initialized successfully!', 'success');
                updateCustomerDropdown();
                refreshInvoices();
                refreshCustomers();
            } else {
                showAlert('InvoicesManager not found!', 'error');
            }
        }

        function updateCustomerDropdown() {
            const dropdown = document.getElementById('customerId');
            dropdown.innerHTML = '<option value="">Select Customer</option>';
            
            if (window.invoicesManager && window.invoicesManager.customers) {
                window.invoicesManager.customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.id;
                    option.textContent = customer.name;
                    dropdown.appendChild(option);
                });
            }
        }

        function refreshInvoices() {
            const tbody = document.getElementById('invoicesTableBody');
            tbody.innerHTML = '';
            
            if (window.invoicesManager && window.invoicesManager.invoices) {
                window.invoicesManager.invoices.forEach(invoice => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${invoice.number}</td>
                        <td>${invoice.customerName}</td>
                        <td>$${invoice.total.toLocaleString()}</td>
                        <td>${new Date(invoice.date).toLocaleDateString()}</td>
                        <td>${new Date(invoice.dueDate).toLocaleDateString()}</td>
                        <td><span style="padding: 4px 8px; border-radius: 4px; background: ${getStatusColor(invoice.status)}; color: white;">${invoice.status.toUpperCase()}</span></td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        function refreshCustomers() {
            const tbody = document.getElementById('customersTableBody');
            tbody.innerHTML = '';
            
            if (window.invoicesManager && window.invoicesManager.customers) {
                window.invoicesManager.customers.forEach(customer => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${customer.name}</td>
                        <td>${customer.email}</td>
                        <td>${customer.phone || 'N/A'}</td>
                        <td>${customer.address || 'N/A'}</td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        function getStatusColor(status) {
            switch(status) {
                case 'paid': return '#28a745';
                case 'pending': return '#ffc107';
                case 'overdue': return '#dc3545';
                default: return '#6c757d';
            }
        }

        function resetForm() {
            document.getElementById('createInvoiceForm').reset();
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('invoiceDate').value = today;
            
            const dueDate = new Date();
            dueDate.setDate(dueDate.getDate() + 30);
            document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];
        }

        // Set up form handler
        document.getElementById('createInvoiceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!window.invoicesManager) {
                showAlert('InvoicesManager not available!', 'error');
                return;
            }
            
            console.log('Form submission intercepted');
            window.invoicesManager.handleCreateInvoice(e);
            
            // Refresh displays after creation
            setTimeout(() => {
                refreshInvoices();
                updateCustomerDropdown();
                resetForm();
            }, 100);
        });

        // Initialize on load
        window.addEventListener('load', function() {
            setTimeout(() => {
                initializeSystem();
                resetForm();
            }, 500);
        });
    </script>
</body>
</html>
