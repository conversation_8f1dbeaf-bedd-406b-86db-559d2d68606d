// Debug script for invoice creation issues
console.log('=== INVOICE DEBUG SCRIPT LOADED ===');

// Function to diagnose invoice creation issues
function diagnoseInvoiceIssues() {
    console.log('\n=== INVOICE SYSTEM DIAGNOSIS ===');
    
    // Check if InvoicesManager exists
    console.log('1. InvoicesManager Check:');
    if (window.invoicesManager) {
        console.log('   ✅ InvoicesManager exists');
        console.log('   📊 Initialized:', window.invoicesManager.isInitialized);
        console.log('   👥 Customers:', window.invoicesManager.customers.length);
        console.log('   📄 Invoices:', window.invoicesManager.invoices.length);
    } else {
        console.log('   ❌ InvoicesManager NOT FOUND');
        return;
    }
    
    // Check modal elements
    console.log('\n2. Modal Elements Check:');
    const modal = document.getElementById('createInvoiceModal');
    console.log('   📋 Create Invoice Modal:', !!modal);
    
    const form = document.getElementById('createInvoiceForm');
    console.log('   📝 Create Invoice Form:', !!form);
    
    const customerDropdown = document.getElementById('invoiceCustomer');
    console.log('   👥 Customer Dropdown:', !!customerDropdown);
    if (customerDropdown) {
        console.log('   📊 Dropdown Options:', customerDropdown.options.length);
    }
    
    // Check form fields
    console.log('\n3. Form Fields Check:');
    const fields = ['invoiceCustomer', 'invoiceAmount', 'invoiceDate', 'dueDate', 'invoiceNotes'];
    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        console.log(`   ${fieldId}:`, !!field);
    });
    
    // Check event listeners
    console.log('\n4. Event Listeners Check:');
    if (form) {
        console.log('   📝 Form onsubmit:', form.onsubmit);
        console.log('   📝 Form event listeners:', form.getEventListeners ? form.getEventListeners() : 'Cannot check');
    }
    
    // Check global functions
    console.log('\n5. Global Functions Check:');
    console.log('   🌐 handleCreateInvoice:', typeof window.handleCreateInvoice);
    console.log('   🌐 showModal:', typeof window.showModal);
    console.log('   🌐 hideModal:', typeof window.hideModal);
    
    console.log('\n=== DIAGNOSIS COMPLETE ===\n');
}

// Function to test invoice creation step by step
function testInvoiceCreationSteps() {
    console.log('\n=== TESTING INVOICE CREATION STEPS ===');
    
    // Step 1: Check system readiness
    if (!window.invoicesManager) {
        console.log('❌ Step 1 FAILED: InvoicesManager not found');
        return;
    }
    console.log('✅ Step 1 PASSED: InvoicesManager found');
    
    // Step 2: Check initialization
    if (!window.invoicesManager.isInitialized) {
        console.log('⚠️ Step 2 WARNING: InvoicesManager not initialized, initializing now...');
        window.invoicesManager.init();
    }
    console.log('✅ Step 2 PASSED: InvoicesManager initialized');
    
    // Step 3: Check customers
    if (window.invoicesManager.customers.length === 0) {
        console.log('❌ Step 3 FAILED: No customers available');
        return;
    }
    console.log('✅ Step 3 PASSED: Customers available:', window.invoicesManager.customers.length);
    
    // Step 4: Test modal opening
    try {
        if (typeof showModal === 'function') {
            console.log('✅ Step 4 PASSED: showModal function available');
        } else {
            console.log('❌ Step 4 FAILED: showModal function not available');
            return;
        }
    } catch (error) {
        console.log('❌ Step 4 FAILED:', error.message);
        return;
    }
    
    // Step 5: Test form submission
    const form = document.getElementById('createInvoiceForm');
    if (!form) {
        console.log('❌ Step 5 FAILED: Form not found');
        return;
    }
    console.log('✅ Step 5 PASSED: Form found');
    
    console.log('\n🎉 ALL STEPS PASSED - System should work!');
    console.log('\n=== TEST COMPLETE ===\n');
}

// Function to manually create an invoice for testing
function manualInvoiceTest() {
    console.log('\n=== MANUAL INVOICE TEST ===');
    
    if (!window.invoicesManager || !window.invoicesManager.isInitialized) {
        console.log('❌ System not ready');
        return;
    }
    
    const testInvoiceData = {
        customerId: window.invoicesManager.customers[0].id,
        amount: 1000.00,
        date: new Date().toISOString().split('T')[0],
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        notes: 'Test invoice created manually'
    };
    
    console.log('Creating test invoice with data:', testInvoiceData);
    
    try {
        const newInvoice = window.invoicesManager.createInvoice(testInvoiceData);
        console.log('✅ Invoice created successfully:', newInvoice);
        return newInvoice;
    } catch (error) {
        console.log('❌ Invoice creation failed:', error);
        return null;
    }
}

// Function to simulate form submission
function simulateFormSubmission() {
    console.log('\n=== SIMULATING FORM SUBMISSION ===');
    
    const form = document.getElementById('createInvoiceForm');
    if (!form) {
        console.log('❌ Form not found');
        return;
    }
    
    // Fill form with test data
    const customerDropdown = document.getElementById('invoiceCustomer');
    const amountField = document.getElementById('invoiceAmount');
    const dateField = document.getElementById('invoiceDate');
    const dueDateField = document.getElementById('dueDate');
    const notesField = document.getElementById('invoiceNotes');
    
    if (customerDropdown && window.invoicesManager.customers.length > 0) {
        customerDropdown.value = window.invoicesManager.customers[0].id;
    }
    if (amountField) amountField.value = '1500.00';
    if (dateField) dateField.value = new Date().toISOString().split('T')[0];
    if (dueDateField) {
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30);
        dueDateField.value = dueDate.toISOString().split('T')[0];
    }
    if (notesField) notesField.value = 'Test invoice via form simulation';
    
    console.log('Form filled with test data');
    
    // Create and dispatch submit event
    const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
    form.dispatchEvent(submitEvent);
    
    console.log('Submit event dispatched');
}

// Auto-run diagnosis when script loads
setTimeout(() => {
    diagnoseInvoiceIssues();
}, 1000);

// Make functions available globally
window.diagnoseInvoiceIssues = diagnoseInvoiceIssues;
window.testInvoiceCreationSteps = testInvoiceCreationSteps;
window.manualInvoiceTest = manualInvoiceTest;
window.simulateFormSubmission = simulateFormSubmission;

console.log('Debug functions available:');
console.log('- diagnoseInvoiceIssues()');
console.log('- testInvoiceCreationSteps()');
console.log('- manualInvoiceTest()');
console.log('- simulateFormSubmission()');
