const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Paths to JSON files
const INVOICES_FILE = path.join(__dirname, 'data', 'invoices.json');
const CUSTOMERS_FILE = path.join(__dirname, 'data', 'customers.json');

// Ensure data directory exists
async function ensureDataDirectory() {
    try {
        await fs.access(path.join(__dirname, 'data'));
    } catch (error) {
        await fs.mkdir(path.join(__dirname, 'data'), { recursive: true });
        console.log('📁 Created data directory');
    }
}

// Read JSON file safely
async function readJSONFile(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.log(`📁 File ${filePath} not found, creating empty array`);
        return [];
    }
}

// Write JSON file safely
async function writeJSONFile(filePath, data) {
    try {
        await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 Successfully wrote to ${filePath}`);
        return true;
    } catch (error) {
        console.error(`❌ Error writing to ${filePath}:`, error);
        return false;
    }
}

// Create backup before writing
async function createBackup(filePath) {
    try {
        const backupPath = filePath.replace('.json', '_backup.json');
        const data = await fs.readFile(filePath, 'utf8');
        await fs.writeFile(backupPath, data, 'utf8');
        console.log(`🔄 Created backup: ${backupPath}`);
    } catch (error) {
        console.log(`⚠️ Could not create backup for ${filePath}`);
    }
}

// API Routes

// Get all invoices
app.get('/api/invoices', async (req, res) => {
    try {
        const invoices = await readJSONFile(INVOICES_FILE);
        res.json(invoices);
    } catch (error) {
        console.error('Error reading invoices:', error);
        res.status(500).json({ error: 'Failed to read invoices' });
    }
});

// Get all customers
app.get('/api/customers', async (req, res) => {
    try {
        const customers = await readJSONFile(CUSTOMERS_FILE);
        res.json(customers);
    } catch (error) {
        console.error('Error reading customers:', error);
        res.status(500).json({ error: 'Failed to read customers' });
    }
});

// Save all invoices
app.post('/api/invoices', async (req, res) => {
    try {
        const invoices = req.body;
        
        if (!Array.isArray(invoices)) {
            return res.status(400).json({ error: 'Invoices must be an array' });
        }

        // Create backup before writing
        await createBackup(INVOICES_FILE);
        
        // Write to file
        const success = await writeJSONFile(INVOICES_FILE, invoices);
        
        if (success) {
            console.log(`💾 Saved ${invoices.length} invoices to JSON file`);
            res.json({ 
                success: true, 
                message: `Saved ${invoices.length} invoices`,
                count: invoices.length 
            });
        } else {
            res.status(500).json({ error: 'Failed to save invoices' });
        }
    } catch (error) {
        console.error('Error saving invoices:', error);
        res.status(500).json({ error: 'Failed to save invoices' });
    }
});

// Save all customers
app.post('/api/customers', async (req, res) => {
    try {
        const customers = req.body;
        
        if (!Array.isArray(customers)) {
            return res.status(400).json({ error: 'Customers must be an array' });
        }

        // Create backup before writing
        await createBackup(CUSTOMERS_FILE);
        
        // Write to file
        const success = await writeJSONFile(CUSTOMERS_FILE, customers);
        
        if (success) {
            console.log(`💾 Saved ${customers.length} customers to JSON file`);
            res.json({ 
                success: true, 
                message: `Saved ${customers.length} customers`,
                count: customers.length 
            });
        } else {
            res.status(500).json({ error: 'Failed to save customers' });
        }
    } catch (error) {
        console.error('Error saving customers:', error);
        res.status(500).json({ error: 'Failed to save customers' });
    }
});

// Add single invoice
app.post('/api/invoices/add', async (req, res) => {
    try {
        const newInvoice = req.body;
        
        // Read existing invoices
        const invoices = await readJSONFile(INVOICES_FILE);
        
        // Add new invoice
        invoices.push(newInvoice);
        
        // Create backup and save
        await createBackup(INVOICES_FILE);
        const success = await writeJSONFile(INVOICES_FILE, invoices);
        
        if (success) {
            console.log(`✅ Added new invoice: ${newInvoice.number}`);
            res.json({ 
                success: true, 
                message: `Added invoice ${newInvoice.number}`,
                invoice: newInvoice 
            });
        } else {
            res.status(500).json({ error: 'Failed to add invoice' });
        }
    } catch (error) {
        console.error('Error adding invoice:', error);
        res.status(500).json({ error: 'Failed to add invoice' });
    }
});

// Add single customer
app.post('/api/customers/add', async (req, res) => {
    try {
        const newCustomer = req.body;
        
        // Read existing customers
        const customers = await readJSONFile(CUSTOMERS_FILE);
        
        // Add new customer
        customers.push(newCustomer);
        
        // Create backup and save
        await createBackup(CUSTOMERS_FILE);
        const success = await writeJSONFile(CUSTOMERS_FILE, customers);
        
        if (success) {
            console.log(`✅ Added new customer: ${newCustomer.name}`);
            res.json({ 
                success: true, 
                message: `Added customer ${newCustomer.name}`,
                customer: newCustomer 
            });
        } else {
            res.status(500).json({ error: 'Failed to add customer' });
        }
    } catch (error) {
        console.error('Error adding customer:', error);
        res.status(500).json({ error: 'Failed to add customer' });
    }
});

// Update invoice
app.put('/api/invoices/:id', async (req, res) => {
    try {
        const invoiceId = parseInt(req.params.id);
        const updateData = req.body;
        
        // Read existing invoices
        const invoices = await readJSONFile(INVOICES_FILE);
        
        // Find and update invoice
        const index = invoices.findIndex(inv => inv.id === invoiceId);
        if (index === -1) {
            return res.status(404).json({ error: 'Invoice not found' });
        }
        
        invoices[index] = { ...invoices[index], ...updateData, updatedAt: new Date().toISOString() };
        
        // Create backup and save
        await createBackup(INVOICES_FILE);
        const success = await writeJSONFile(INVOICES_FILE, invoices);
        
        if (success) {
            console.log(`✅ Updated invoice: ${invoices[index].number}`);
            res.json({ 
                success: true, 
                message: `Updated invoice ${invoices[index].number}`,
                invoice: invoices[index] 
            });
        } else {
            res.status(500).json({ error: 'Failed to update invoice' });
        }
    } catch (error) {
        console.error('Error updating invoice:', error);
        res.status(500).json({ error: 'Failed to update invoice' });
    }
});

// Update customer
app.put('/api/customers/:id', async (req, res) => {
    try {
        const customerId = parseInt(req.params.id);
        const updateData = req.body;
        
        // Read existing customers
        const customers = await readJSONFile(CUSTOMERS_FILE);
        
        // Find and update customer
        const index = customers.findIndex(cust => cust.id === customerId);
        if (index === -1) {
            return res.status(404).json({ error: 'Customer not found' });
        }
        
        customers[index] = { ...customers[index], ...updateData, updatedAt: new Date().toISOString() };
        
        // Create backup and save
        await createBackup(CUSTOMERS_FILE);
        const success = await writeJSONFile(CUSTOMERS_FILE, customers);
        
        if (success) {
            console.log(`✅ Updated customer: ${customers[index].name}`);
            res.json({ 
                success: true, 
                message: `Updated customer ${customers[index].name}`,
                customer: customers[index] 
            });
        } else {
            res.status(500).json({ error: 'Failed to update customer' });
        }
    } catch (error) {
        console.error('Error updating customer:', error);
        res.status(500).json({ error: 'Failed to update customer' });
    }
});

// Delete invoice (soft delete)
app.delete('/api/invoices/:id', async (req, res) => {
    try {
        const invoiceId = parseInt(req.params.id);
        
        // Read existing invoices
        const invoices = await readJSONFile(INVOICES_FILE);
        
        // Find and soft delete invoice
        const index = invoices.findIndex(inv => inv.id === invoiceId);
        if (index === -1) {
            return res.status(404).json({ error: 'Invoice not found' });
        }
        
        invoices[index].isActive = false;
        invoices[index].deletedAt = new Date().toISOString();
        
        // Create backup and save
        await createBackup(INVOICES_FILE);
        const success = await writeJSONFile(INVOICES_FILE, invoices);
        
        if (success) {
            console.log(`🗑️ Soft deleted invoice: ${invoices[index].number}`);
            res.json({ 
                success: true, 
                message: `Deleted invoice ${invoices[index].number}` 
            });
        } else {
            res.status(500).json({ error: 'Failed to delete invoice' });
        }
    } catch (error) {
        console.error('Error deleting invoice:', error);
        res.status(500).json({ error: 'Failed to delete invoice' });
    }
});

// Delete customer (soft delete)
app.delete('/api/customers/:id', async (req, res) => {
    try {
        const customerId = parseInt(req.params.id);
        
        // Read existing customers
        const customers = await readJSONFile(CUSTOMERS_FILE);
        
        // Find and soft delete customer
        const index = customers.findIndex(cust => cust.id === customerId);
        if (index === -1) {
            return res.status(404).json({ error: 'Customer not found' });
        }
        
        customers[index].isActive = false;
        customers[index].deletedAt = new Date().toISOString();
        
        // Create backup and save
        await createBackup(CUSTOMERS_FILE);
        const success = await writeJSONFile(CUSTOMERS_FILE, customers);
        
        if (success) {
            console.log(`🗑️ Soft deleted customer: ${customers[index].name}`);
            res.json({ 
                success: true, 
                message: `Deleted customer ${customers[index].name}` 
            });
        } else {
            res.status(500).json({ error: 'Failed to delete customer' });
        }
    } catch (error) {
        console.error('Error deleting customer:', error);
        res.status(500).json({ error: 'Failed to delete customer' });
    }
});

// Health check
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        message: 'Accounting Software API is running',
        timestamp: new Date().toISOString()
    });
});

// Start server
async function startServer() {
    try {
        await ensureDataDirectory();
        
        app.listen(PORT, () => {
            console.log('🚀 ================================');
            console.log('🚀 Accounting Software Backend API');
            console.log('🚀 ================================');
            console.log(`🚀 Server running on http://localhost:${PORT}`);
            console.log(`🚀 API endpoints available at http://localhost:${PORT}/api/`);
            console.log(`🚀 Frontend available at http://localhost:${PORT}/index.html`);
            console.log('🚀 ================================');
        });
    } catch (error) {
        console.error('❌ Failed to start server:', error);
    }
}

startServer();
