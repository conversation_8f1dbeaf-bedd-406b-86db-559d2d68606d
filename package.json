{"name": "accounting-software-backend", "version": "1.0.0", "description": "Backend API for Accounting Software with JSON file storage", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-deps": "npm install express cors", "setup": "npm install && node server.js"}, "keywords": ["accounting", "invoices", "customers", "json", "api", "backend"], "author": "Accounting Software", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}