/**
 * Dashboard.js - Comprehensive Dashboard Management System
 * Manages all dashboard functionality including charts, metrics, and real-time updates
 */

class DashboardManager {
    constructor() {
        this.charts = {};
        this.refreshInterval = null;
        this.updateInterval = 5 * 60 * 1000; // 5 minutes
        this.isInitialized = false;
    }

    /**
     * Initialize the dashboard with all components
     */
    init() {
        if (this.isInitialized) return;
        
        this.loadDashboardMetrics();
        this.initializeCharts();
        this.setupRealTimeUpdates();
        this.loadRecentActivities();
        this.loadAlerts();
        this.setupEventListeners();
        
        this.isInitialized = true;
        console.log('Dashboard initialized successfully');
    }

    /**
     * Load and display dashboard metrics
     */
    loadDashboardMetrics() {
        const metrics = this.calculateMetrics();

        // Update metric cards
        this.updateMetricCard('totalRevenue', metrics.totalRevenue, '+12.5%', 'positive');
        this.updateMetricCard('totalExpenses', metrics.totalExpenses, '+8.2%', 'negative');
        this.updateMetricCard('netProfit', metrics.netProfit, '+15.3%', 'positive');
        this.updatePendingRequestsCard(metrics.pendingRequests);
        this.updateMetricCard('cashFlow', metrics.cashFlow, '+5.8%', 'positive');
        this.updateMetricCard('activeEmployees', metrics.activeEmployees, '+3', 'positive');
    }

    /**
     * Calculate real-time metrics from data
     */
    calculateMetrics() {
        const invoices = this.getInvoicesData();
        const expenses = this.getExpensesData();
        const employees = this.getEmployeesData();

        // Calculate pending invoices data
        const pendingInvoices = invoices.filter(inv => inv.status === 'pending');
        const pendingCount = pendingInvoices.length;
        const pendingAmount = pendingInvoices.reduce((sum, inv) => sum + (inv.total || inv.amount || 0), 0);

        const totalRevenue = invoices.reduce((sum, inv) => sum + (inv.status === 'paid' ? (inv.total || inv.amount || 0) : 0), 0);
        const totalExpenses = expenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);
        const netProfit = totalRevenue - totalExpenses;
        const cashFlow = totalRevenue - totalExpenses;
        const activeEmployees = employees.filter(emp => emp.isActive).length;

        return {
            totalRevenue,
            totalExpenses,
            netProfit,
            pendingRequests: {
                count: pendingCount,
                amount: pendingAmount
            },
            cashFlow,
            activeEmployees
        };
    }

    /**
     * Update individual metric card
     */
    updateMetricCard(metricId, value, change, changeType) {
        const card = document.querySelector(`[data-metric="${metricId}"]`);
        if (card) {
            const valueElement = card.querySelector('.metric-value');
            const changeElement = card.querySelector('.metric-change');

            if (valueElement) {
                valueElement.textContent = typeof value === 'number' ?
                    (metricId.includes('Revenue') || metricId.includes('Expenses') || metricId.includes('Profit') || metricId.includes('Flow') ?
                        '$' + value.toLocaleString() : value.toString()) : value;
            }

            if (changeElement) {
                changeElement.textContent = change;
                changeElement.className = `metric-change ${changeType}`;
            }
        }
    }

    /**
     * Update pending requests card with count and amount
     */
    updatePendingRequestsCard(pendingData) {
        const countElement = document.getElementById('pendingRequestsCount');
        const amountElement = document.getElementById('pendingRequestsAmount');

        if (countElement) {
            countElement.textContent = pendingData.count.toString();
        }

        if (amountElement) {
            amountElement.textContent = '$' + pendingData.amount.toLocaleString();

            // Update styling based on amount
            if (pendingData.amount > 0) {
                amountElement.className = 'metric-change warning';
            } else {
                amountElement.className = 'metric-change neutral';
            }
        }

        // Update the card icon color and animation based on pending status
        const card = document.querySelector('[data-metric="pendingRequests"]');
        if (card) {
            const icon = card.querySelector('.metric-icon');
            if (icon) {
                if (pendingData.count > 0) {
                    icon.className = 'metric-icon warning';
                    card.classList.add('has-pending');
                } else {
                    icon.className = 'metric-icon success';
                    card.classList.remove('has-pending');
                }
            }
        }

        console.log('Pending requests updated:', pendingData);
    }

    /**
     * Initialize all dashboard charts
     */
    initializeCharts() {
        this.initRevenueChart();
        this.initExpenseChart();
        this.initCashFlowChart();
        this.initPayrollChart();
        this.initProfitTrendChart();
        // Initialize new Financial Overview charts
        this.initFinancialOverviewCharts();
    }

    /**
     * Initialize Financial Overview Charts (same as Reports)
     */
    initFinancialOverviewCharts() {
        setTimeout(() => {
            this.initExpensePieChart();
            this.initNetProfitChart();
            this.initTotalExpensesChart();
        }, 200);
    }

    /**
     * Initialize Revenue vs Expenses Chart
     */
    initRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;

        const data = this.getRevenueExpenseData();
        
        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Revenue',
                    data: data.revenue,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Expenses',
                    data: data.expenses,
                    borderColor: '#f44336',
                    backgroundColor: 'rgba(244, 67, 54, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Revenue vs Expenses Trend'
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Initialize Expense Breakdown Chart
     */
    initExpenseChart() {
        const ctx = document.getElementById('expenseChart');
        if (!ctx) return;

        const data = this.getExpenseBreakdownData();
        
        this.charts.expense = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Expense Breakdown by Category'
                    },
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    }

    /**
     * Initialize Cash Flow Chart
     */
    initCashFlowChart() {
        const ctx = document.getElementById('cashFlowChart');
        if (!ctx) return;

        const data = this.getCashFlowData();
        
        this.charts.cashFlow = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Cash Inflow',
                    data: data.inflow,
                    backgroundColor: '#4CAF50'
                }, {
                    label: 'Cash Outflow',
                    data: data.outflow,
                    backgroundColor: '#f44336'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Monthly Cash Flow'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Initialize Payroll Chart
     */
    initPayrollChart() {
        const ctx = document.getElementById('payrollChart');
        if (!ctx) return;

        const data = this.getPayrollData();
        
        this.charts.payroll = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Gross Payroll',
                    data: data.gross,
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Net Payroll',
                    data: data.net,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Payroll Trends'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Initialize Profit Trend Chart
     */
    initProfitTrendChart() {
        const ctx = document.getElementById('profitTrendChart');
        if (!ctx) return;

        const data = this.getProfitTrendData();
        
        this.charts.profitTrend = new Chart(ctx, {
            type: 'area',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Net Profit',
                    data: data.profit,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.2)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Profit Trend Analysis'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Initialize Expense Pie Chart for Financial Overview
     */
    initExpensePieChart() {
        const canvas = document.getElementById('expensePieChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.getCurrentMonthExpenseData();
        const currentMonth = new Date().toLocaleString('default', { month: 'long' });

        this.charts.expensePie = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: `${currentMonth} ${new Date().getFullYear()}`,
                        font: {
                            size: 14,
                            weight: 'normal'
                        },
                        color: '#6b7280',
                        padding: {
                            bottom: 20
                        }
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${context.label}: $${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Initialize Net Profit Chart for Financial Overview
     */
    initNetProfitChart() {
        const canvas = document.getElementById('netProfitChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.getNetProfitData();

        this.charts.netProfit = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Net Profit',
                    data: data.profit,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true,
                    borderWidth: 3,
                    pointBackgroundColor: '#10b981',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    /**
     * Initialize Total Expenses Chart for Financial Overview
     */
    initTotalExpensesChart() {
        const canvas = document.getElementById('totalExpensesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.getTotalExpensesData();

        this.charts.totalExpenses = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Total Expenses',
                    data: data.expenses,
                    backgroundColor: '#f59e0b',
                    borderColor: '#d97706',
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    /**
     * Get sample data for charts (replace with real data integration)
     */
    getRevenueExpenseData() {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        return {
            labels: months,
            revenue: [45000, 52000, 48000, 61000, 55000, 67000],
            expenses: [32000, 38000, 35000, 42000, 39000, 45000]
        };
    }

    getExpenseBreakdownData() {
        return {
            labels: ['Salaries', 'Office Supplies', 'Marketing', 'Utilities', 'Travel', 'Software'],
            values: [45000, 8000, 12000, 5000, 7000, 3000]
        };
    }

    getCurrentMonthExpenseData() {
        return {
            labels: ['Office Supplies', 'Travel', 'Marketing', 'Utilities', 'Software', 'Other'],
            values: [850, 420, 1200, 380, 250, 450]
        };
    }

    getCashFlowData() {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        return {
            labels: months,
            inflow: [45000, 52000, 48000, 61000, 55000, 67000],
            outflow: [32000, 38000, 35000, 42000, 39000, 45000]
        };
    }

    getPayrollData() {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        return {
            labels: months,
            gross: [125000, 128000, 130000, 132000, 135000, 138000],
            net: [95000, 97000, 98500, 100000, 102000, 104000]
        };
    }

    getProfitTrendData() {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        return {
            labels: months,
            profit: [13000, 14000, 13000, 19000, 16000, 22000]
        };
    }

    getNetProfitData() {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return {
            labels: months,
            profit: [4000, 6000, 7000, 4000, 7000, 7000, 8500, 9200, 8800, 10500, 11200, 12000]
        };
    }

    getTotalExpensesData() {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return {
            labels: months,
            expenses: [8000, 9000, 11000, 10000, 13000, 15000, 14500, 16200, 15800, 17500, 18200, 19000]
        };
    }

    /**
     * Load recent activities
     */
    loadRecentActivities() {
        const activities = [
            { type: 'invoice', description: 'Invoice INV-001 created for $5,000', time: '2 hours ago', icon: 'fas fa-file-invoice' },
            { type: 'payment', description: 'Payment received for INV-045', time: '4 hours ago', icon: 'fas fa-credit-card' },
            { type: 'expense', description: 'Office supplies expense added', time: '6 hours ago', icon: 'fas fa-receipt' },
            { type: 'payroll', description: 'Monthly payroll processed', time: '1 day ago', icon: 'fas fa-users' },
            { type: 'user', description: 'New user account created', time: '2 days ago', icon: 'fas fa-user-plus' }
        ];

        const container = document.getElementById('recentActivities');
        if (container) {
            container.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <p class="activity-description">${activity.description}</p>
                        <span class="activity-time">${activity.time}</span>
                    </div>
                </div>
            `).join('');
        }
    }

    /**
     * Load system alerts
     */
    loadAlerts() {
        const alerts = [
            { type: 'warning', message: '5 invoices are overdue', action: 'View Invoices' },
            { type: 'info', message: 'Monthly backup completed successfully', action: 'View Details' },
            { type: 'success', message: 'Payroll for June processed', action: 'View Report' }
        ];

        const container = document.getElementById('systemAlerts');
        if (container) {
            container.innerHTML = alerts.map(alert => `
                <div class="alert ${alert.type}">
                    <span>${alert.message}</span>
                    <button class="alert-action">${alert.action}</button>
                </div>
            `).join('');
        }
    }

    /**
     * Setup real-time updates
     */
    setupRealTimeUpdates() {
        // Update dashboard every 5 minutes
        this.refreshInterval = setInterval(() => {
            this.loadDashboardMetrics();
            this.updateCharts();
            console.log('Dashboard updated automatically');
        }, this.updateInterval);
    }

    /**
     * Update all charts with new data
     */
    updateCharts() {
        Object.keys(this.charts).forEach(chartKey => {
            if (this.charts[chartKey]) {
                // Update chart data here
                this.charts[chartKey].update();
            }
        });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Refresh button
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refresh();
            });
        }

        // Export dashboard
        const exportBtn = document.getElementById('exportDashboard');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportDashboard();
            });
        }
    }

    /**
     * Refresh dashboard manually
     */
    refresh() {
        this.loadDashboardMetrics();
        this.updateCharts();
        this.loadRecentActivities();
        this.loadAlerts();
        
        // Show refresh feedback
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            const originalText = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
            refreshBtn.disabled = true;
            
            setTimeout(() => {
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            }, 1000);
        }
    }

    /**
     * Export dashboard data
     */
    exportDashboard() {
        const metrics = this.calculateMetrics();
        const exportData = {
            timestamp: new Date().toISOString(),
            metrics: metrics,
            charts: {
                revenue: this.getRevenueExpenseData(),
                expenses: this.getExpenseBreakdownData(),
                cashFlow: this.getCashFlowData(),
                payroll: this.getPayrollData()
            }
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `dashboard-export-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    /**
     * Get data from other modules
     */
    getInvoicesData() {
        // Integration with Invoices.js
        if (window.invoicesManager && window.invoicesManager.getAllInvoices) {
            const invoices = window.invoicesManager.getAllInvoices();
            console.log('Dashboard: Retrieved invoices from InvoicesManager:', invoices.length);
            return invoices;
        }

        // Fallback to localStorage
        const savedInvoices = localStorage.getItem('accounting_invoices');
        if (savedInvoices) {
            try {
                const invoices = JSON.parse(savedInvoices);
                console.log('Dashboard: Retrieved invoices from localStorage:', invoices.length);
                return invoices;
            } catch (error) {
                console.error('Dashboard: Error parsing invoices from localStorage:', error);
            }
        }

        console.log('Dashboard: No invoice data available');
        return [];
    }

    getExpensesData() {
        // Integration with Expenses.js
        return window.expensesManager ? window.expensesManager.getAllExpenses() : [];
    }

    getEmployeesData() {
        // Integration with Payroll.js
        return window.payrollManager ? window.payrollManager.getAllEmployees() : [];
    }

    /**
     * Cleanup when dashboard is destroyed
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        Object.keys(this.charts).forEach(chartKey => {
            if (this.charts[chartKey]) {
                this.charts[chartKey].destroy();
            }
        });
        
        this.isInitialized = false;
    }
}

// Create global instance
window.dashboardManager = new DashboardManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.dashboardManager.init();
    });
} else {
    window.dashboardManager.init();
}
