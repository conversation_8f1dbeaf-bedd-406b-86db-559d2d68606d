@echo off
echo ================================
echo Accounting Software Backend Setup
echo ================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js is installed
echo.

REM Check if package.json exists
if not exist package.json (
    echo ERROR: package.json not found!
    echo Make sure you're running this from the correct directory.
    pause
    exit /b 1
)

echo Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo Dependencies installed successfully!
echo.
echo Starting the backend server...
echo.
echo ================================
echo Backend API will be available at:
echo http://localhost:3000
echo.
echo Frontend will be available at:
echo http://localhost:3000/index.html
echo ================================
echo.
echo Press Ctrl+C to stop the server
echo.

node server.js

pause
