# 🚀 Backend Setup Instructions for Permanent JSON Storage

## 📋 **OVERVIEW**

Your accounting software now has a **COMPLETE BACKEND SYSTEM** that saves all invoices and customers directly to the actual JSON files (`data/invoices.json` and `data/customers.json`). When users create data through the frontend, it gets permanently stored in the backend JSON files.

## 🔧 **SETUP REQUIREMENTS**

### **1. Install Node.js**
- Download and install Node.js from: https://nodejs.org/
- Choose the LTS (Long Term Support) version
- Verify installation by opening Command Prompt and typing: `node --version`

### **2. Install Dependencies**
Open Command Prompt in your project folder and run:
```bash
npm install
```

This will install:
- `express` - Web server framework
- `cors` - Cross-origin resource sharing

## 🚀 **STARTING THE BACKEND**

### **Option 1: Using the Batch File (Windows)**
1. Double-click `start-backend.bat`
2. The script will automatically install dependencies and start the server

### **Option 2: Manual Command Line**
1. Open Command Prompt in your project folder
2. Run: `npm install` (first time only)
3. Run: `node server.js`

### **Option 3: Development Mode (Auto-restart)**
1. Install nodemon: `npm install -g nodemon`
2. Run: `npm run dev`

## 🌐 **ACCESSING THE APPLICATION**

Once the backend is running:

- **Backend API**: http://localhost:3000/api/
- **Frontend Application**: http://localhost:3000/index.html
- **Health Check**: http://localhost:3000/api/health

## 📊 **HOW IT WORKS**

### **Data Flow:**
```
Frontend (Create Invoice/Customer)
    ↓
FileStorageManager (API Call)
    ↓
Backend Server (Express.js)
    ↓
JSON File (data/invoices.json or data/customers.json)
    ↓
Permanent Storage ✅
```

### **Dual Storage System:**
1. **Primary**: Backend API writes to actual JSON files
2. **Fallback**: localStorage backup if backend is unavailable

## 🔗 **API ENDPOINTS**

### **Invoices:**
- `GET /api/invoices` - Get all invoices
- `POST /api/invoices` - Save all invoices
- `POST /api/invoices/add` - Add single invoice
- `PUT /api/invoices/:id` - Update invoice
- `DELETE /api/invoices/:id` - Soft delete invoice

### **Customers:**
- `GET /api/customers` - Get all customers
- `POST /api/customers` - Save all customers
- `POST /api/customers/add` - Add single customer
- `PUT /api/customers/:id` - Update customer
- `DELETE /api/customers/:id` - Soft delete customer

### **System:**
- `GET /api/health` - Check if backend is running

## 📁 **FILE STRUCTURE**

```
📁 Accounting Software Web Version/
├── 📄 server.js                    # Backend server
├── 📄 package.json                 # Dependencies
├── 📄 start-backend.bat           # Windows startup script
├── 📄 index.html                  # Frontend application
├── 📁 JS/
│   └── FileStorageManager.js      # Enhanced with API integration
├── 📁 data/
│   ├── invoices.json              # Permanent invoice storage
│   ├── customers.json             # Permanent customer storage
│   ├── invoices_backup.json       # Auto-generated backups
│   └── customers_backup.json      # Auto-generated backups
└── 📁 CSS/
    └── styles.css                 # Frontend styles
```

## ✅ **VERIFICATION STEPS**

### **1. Check Backend is Running:**
- Open browser and go to: http://localhost:3000/api/health
- Should see: `{"status":"OK","message":"Accounting Software API is running"}`

### **2. Test Data Persistence:**
1. Create an invoice through the frontend
2. Check `data/invoices.json` - should contain the new invoice
3. Refresh the page - invoice should still be there
4. Restart the backend - invoice should still be there

### **3. Check Console Logs:**
- Backend console should show: `✅ Invoice data saved to backend JSON file`
- Frontend console should show: `✅ Loaded invoice data from backend API`

## 🔒 **BACKUP SYSTEM**

### **Automatic Backups:**
- Before every save, a backup is created
- Backup files: `invoices_backup.json`, `customers_backup.json`
- localStorage also maintains backup copies

### **Recovery:**
- If main JSON file is corrupted, backup is automatically used
- If backend is down, localStorage fallback is used
- Multiple layers of data protection

## 🛠️ **TROUBLESHOOTING**

### **Backend Won't Start:**
1. Check if Node.js is installed: `node --version`
2. Install dependencies: `npm install`
3. Check if port 3000 is available
4. Run: `node server.js` and check for error messages

### **Frontend Can't Connect to Backend:**
1. Verify backend is running at http://localhost:3000
2. Check browser console for CORS errors
3. Ensure both frontend and backend are on same domain

### **Data Not Saving:**
1. Check backend console for error messages
2. Verify JSON files have write permissions
3. Check if `data/` directory exists
4. Look for API error responses in browser network tab

## 📈 **PRODUCTION DEPLOYMENT**

For production use:
1. Change `apiBaseUrl` in FileStorageManager.js to your server URL
2. Use environment variables for configuration
3. Add authentication and authorization
4. Use a proper database instead of JSON files
5. Add rate limiting and security headers

## 🎯 **FEATURES**

### **✅ Implemented:**
- ✅ Permanent JSON file storage
- ✅ Automatic backups before saves
- ✅ Dual storage (API + localStorage)
- ✅ Soft delete functionality
- ✅ CRUD operations for invoices and customers
- ✅ Error handling and fallbacks
- ✅ Real-time data synchronization

### **🔄 Auto-Features:**
- 🔄 Auto-backup creation
- 🔄 Auto-recovery from backups
- 🔄 Auto-fallback to localStorage
- 🔄 Auto-save every 30 seconds
- 🔄 Auto-save before page unload

## 🎉 **SUCCESS!**

Your accounting software now has **PERMANENT JSON STORAGE**! 

- ✅ All invoices and customers are saved to actual JSON files
- ✅ Data persists across browser refreshes and restarts
- ✅ Automatic backups prevent data loss
- ✅ Dual storage system ensures reliability
- ✅ Easy to setup and run

**Your data will NEVER be lost!** 🔒
