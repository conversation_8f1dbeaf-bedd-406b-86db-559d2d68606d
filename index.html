<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Accounting Software - Complete Application</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="CSS/styles.css">
</head>
<body id="app">
    <!-- Login Page -->
    <div id="loginPage" class="login-page">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <div class="logo">
                        <i class="fas fa-calculator"></i>
                        <h1>Enterprise Accounting</h1>
                    </div>
                    <p class="subtitle">Secure Financial Management System</p>
                </div>

                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="credential">
                            <i class="fas fa-user"></i>
                            Username or Email
                        </label>
                        <input
                            type="text"
                            id="credential"
                            name="credential"
                            required
                            placeholder="Default: admin"
                            value="admin"
                        >
                    </div>

                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            Password
                        </label>
                        <div class="password-input">
                            <input
                                type="password"
                                id="password"
                                name="password"
                                required
                                placeholder="Default: admin"
                                value="admin"
                            >
                            <button type="button" class="toggle-password" onclick="togglePassword()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </button>
                </form>

                <div class="alert info mt-4">
                    <span>💡 Demo Mode: Use admin/admin to login. Only admins can create new users.</span>
                </div>

                <!-- Alert Messages -->
                <div class="alert hidden" id="alertMessage">
                    <span class="alert-text"></span>
                    <button class="alert-close" onclick="hideAlert()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Page -->
    <div id="dashboardPage" class="dashboard-page hidden">
        <!-- Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-calculator"></i>
                    <span>Enterprise Accounting</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <div class="sidebar-menu">
                <ul class="menu-list">
                    <li class="menu-item active">
                        <a href="#" class="menu-link" onclick="showPage('dashboard')">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showPage('invoices')">
                            <i class="fas fa-file-invoice"></i>
                            <span>Invoices</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showPage('customers')">
                            <i class="fas fa-users"></i>
                            <span>Customers</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showPage('expenses')">
                            <i class="fas fa-receipt"></i>
                            <span>Expenses</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showPage('payroll')">
                            <i class="fas fa-users"></i>
                            <span>Payroll</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showPage('reports')">
                            <i class="fas fa-chart-bar"></i>
                            <span>Reports</span>
                        </a>
                    </li>

                    <li class="menu-item admin-only">
                        <a href="#" class="menu-link" onclick="showPage('users')">
                            <i class="fas fa-users-cog"></i>
                            <span>User Management</span>
                        </a>
                    </li>
                    <li class="menu-item admin-only">
                        <a href="#" class="menu-link" onclick="showPage('settings')">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name" id="userName">System Administrator</span>
                        <span class="user-role" id="userRole">admin</span>
                    </div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Header -->
            <header class="top-header">
                <div class="header-left">
                    <h1 class="page-title" id="pageTitle">Dashboard</h1>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <!-- Theme toggle removed -->
                    </div>
                </div>
            </header>

            <!-- Page Content Container -->
            <div id="pageContent" class="dashboard-content">
                <!-- Dashboard Content will be loaded here -->
            </div>
        </main>
    </div>

    <!-- Modals -->
    <!-- Create User Modal -->
    <div class="modal-overlay hidden" id="createUserModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> Create New User</h3>
                <button class="modal-close" onclick="hideModal('createUserModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createUserForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="newFirstName">First Name</label>
                        <input type="text" id="newFirstName" name="firstName" required>
                    </div>
                    <div class="form-group">
                        <label for="newLastName">Last Name</label>
                        <input type="text" id="newLastName" name="lastName" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="newUsername">Username</label>
                    <input type="text" id="newUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="newEmail">Email</label>
                    <input type="email" id="newEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="newPhone">Phone (Optional)</label>
                    <input type="tel" id="newPhone" name="phone">
                </div>
                <div class="form-group">
                    <label for="newRole">Role</label>
                    <select id="newRole" name="role" required>
                        <option value="employee">Employee</option>
                        <option value="accountant">Accountant</option>
                        <option value="manager">Manager</option>
                        <option value="auditor">Auditor</option>
                        <option value="admin">Administrator</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="newPassword">Password</label>
                    <input type="password" id="newPassword" name="password" required>
                </div>
                <div class="form-group">
                    <label for="confirmNewPassword">Confirm Password</label>
                    <input type="password" id="confirmNewPassword" name="confirmPassword" required>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('createUserModal')">Cancel</button>
                <button type="submit" form="createUserForm" class="btn btn-primary">Create User</button>
            </div>
        </div>
    </div>

    <!-- Create Invoice Modal -->
    <div class="modal-overlay hidden" id="createInvoiceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-file-invoice"></i> Create New Invoice</h3>
                <button class="modal-close" onclick="hideModal('createInvoiceModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createInvoiceForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="invoiceCustomer">Customer</label>
                        <select id="invoiceCustomer" name="customerId" required>
                            <option value="">Select Customer</option>
                            <!-- Options will be populated dynamically from Customer Management -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="invoiceDate">Invoice Date</label>
                        <input type="date" id="invoiceDate" name="invoiceDate" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="dueDate">Due Date</label>
                        <input type="date" id="dueDate" name="dueDate" required>
                    </div>
                    <div class="form-group">
                        <label for="invoiceAmount">Amount</label>
                        <input type="number" id="invoiceAmount" name="amount" step="0.01" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="invoiceNotes">Notes</label>
                    <textarea id="invoiceNotes" name="notes" rows="3"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('createInvoiceModal')">Cancel</button>
                <button type="submit" form="createInvoiceForm" class="btn btn-primary">Create Invoice</button>
            </div>
        </div>
    </div>

    <!-- Create Expense Modal -->
    <div class="modal-overlay hidden" id="createExpenseModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-receipt"></i> Add New Expense</h3>
                <button class="modal-close" onclick="hideModal('createExpenseModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createExpenseForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="expenseDate">Expense Date</label>
                        <input type="date" id="expenseDate" name="expenseDate" required>
                    </div>
                    <div class="form-group">
                        <label for="expenseAmount">Amount</label>
                        <input type="number" id="expenseAmount" name="amount" step="0.01" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="expenseCategory">Category</label>
                    <select id="expenseCategory" name="category" required>
                        <option value="">Select Category</option>
                        <option value="office-supplies">Office Supplies</option>
                        <option value="travel">Travel</option>
                        <option value="marketing">Marketing</option>
                        <option value="utilities">Utilities</option>
                        <option value="software">Software</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="expenseDescription">Description</label>
                    <textarea id="expenseDescription" name="description" rows="3" required></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('createExpenseModal')">Cancel</button>
                <button type="submit" form="createExpenseForm" class="btn btn-primary">Add Expense</button>
            </div>
        </div>
    </div>

    <!-- Payroll Management Modals -->
    <!-- Calculate Payroll Modal -->
    <div class="modal-overlay hidden" id="calculatePayrollModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-calculator"></i> Calculate Payroll</h3>
                <button class="modal-close" onclick="hideModal('calculatePayrollModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="payPeriodStart">Pay Period Start</label>
                        <input type="date" id="payPeriodStart" required>
                    </div>
                    <div class="form-group">
                        <label for="payPeriodEnd">Pay Period End</label>
                        <input type="date" id="payPeriodEnd" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="payrollType">Payroll Type</label>
                    <select id="payrollType" required>
                        <option value="regular">Regular Payroll</option>
                        <option value="bonus">Bonus Payroll</option>
                        <option value="overtime">Overtime Payroll</option>
                        <option value="final">Final Settlement</option>
                    </select>
                </div>
                <div class="alert info">
                    <span>💡 This will calculate payroll for all active employees for the selected period.</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('calculatePayrollModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="calculatePayroll()">Calculate Payroll</button>
            </div>
        </div>
    </div>

    <!-- Employee Management Modal -->
    <div class="modal-overlay hidden" id="employeeManagementModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> Employee Management</h3>
                <button class="modal-close" onclick="hideModal('employeeManagementModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="empFirstName">First Name</label>
                        <input type="text" id="empFirstName" required>
                    </div>
                    <div class="form-group">
                        <label for="empLastName">Last Name</label>
                        <input type="text" id="empLastName" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="empEmail">Email</label>
                        <input type="email" id="empEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="empPhone">Phone</label>
                        <input type="tel" id="empPhone">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="empPosition">Position</label>
                        <input type="text" id="empPosition" required>
                    </div>
                    <div class="form-group">
                        <label for="empDepartment">Department</label>
                        <select id="empDepartment" required>
                            <option value="">Select Department</option>
                            <option value="accounting">Accounting</option>
                            <option value="hr">Human Resources</option>
                            <option value="it">Information Technology</option>
                            <option value="sales">Sales</option>
                            <option value="marketing">Marketing</option>
                            <option value="operations">Operations</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="empBasicSalary">Basic Salary</label>
                        <input type="number" id="empBasicSalary" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="empHireDate">Hire Date</label>
                        <input type="date" id="empHireDate" required>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('employeeManagementModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveEmployee()">Save Employee</button>
            </div>
        </div>
    </div>

    <!-- Advanced Settings Modals -->
    <!-- Company Settings Modal -->
    <div class="modal-overlay hidden" id="companySettingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-building"></i> Company Settings</h3>
                <button class="modal-close" onclick="hideModal('companySettingsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="modal-body">
                <div class="form-group">
                    <label for="companyName">Company Name</label>
                    <input type="text" id="companyName" value="Enterprise Accounting Solutions" required>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="companyEmail">Email</label>
                        <input type="email" id="companyEmail" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="companyPhone">Phone</label>
                        <input type="tel" id="companyPhone" value="******-0123">
                    </div>
                </div>
                <div class="form-group">
                    <label for="companyAddress">Address</label>
                    <textarea id="companyAddress" rows="3">123 Business Street, Suite 100
City, State 12345
United States</textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="taxId">Tax ID</label>
                        <input type="text" id="taxId" value="12-3456789">
                    </div>
                    <div class="form-group">
                        <label for="registrationNumber">Registration Number</label>
                        <input type="text" id="registrationNumber" value="REG123456">
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('companySettingsModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveCompanySettings()">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Tax Configuration Modal -->
    <div class="modal-overlay hidden" id="taxConfigModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-percentage"></i> Tax Configuration</h3>
                <button class="modal-close" onclick="hideModal('taxConfigModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="defaultTaxRate">Default Tax Rate (%)</label>
                        <input type="number" id="defaultTaxRate" value="15" step="0.01" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label for="vatRate">VAT/GST Rate (%)</label>
                        <input type="number" id="vatRate" value="10" step="0.01" min="0" max="100">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="incomeTaxRate">Income Tax Rate (%)</label>
                        <input type="number" id="incomeTaxRate" value="20" step="0.01" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label for="socialSecurityRate">Social Security Rate (%)</label>
                        <input type="number" id="socialSecurityRate" value="6.2" step="0.01" min="0" max="100">
                    </div>
                </div>
                <div class="form-group">
                    <label for="taxYear">Tax Year</label>
                    <select id="taxYear">
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                        <option value="2022">2022</option>
                    </select>
                </div>
                <div class="alert info">
                    <span>💡 These rates will be applied to all new transactions and payroll calculations.</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('taxConfigModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveTaxConfiguration()">Save Configuration</button>
            </div>
        </div>
    </div>

    <!-- Custom Report Builder Modal -->
    <div class="modal-overlay hidden" id="customReportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-cogs"></i> Custom Report Builder</h3>
                <button class="modal-close" onclick="hideModal('customReportModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="reportName">Report Name</label>
                    <input type="text" id="reportName" placeholder="Enter report name">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="reportDateFrom">Date From</label>
                        <input type="date" id="reportDateFrom">
                    </div>
                    <div class="form-group">
                        <label for="reportDateTo">Date To</label>
                        <input type="date" id="reportDateTo">
                    </div>
                </div>
                <div class="form-group">
                    <label for="reportDataSource">Data Source</label>
                    <select id="reportDataSource" multiple style="height: 120px;">
                        <option value="invoices">Invoices</option>
                        <option value="expenses">Expenses</option>
                        <option value="payments">Payments</option>
                        <option value="customers">Customers</option>
                        <option value="suppliers">Suppliers</option>
                        <option value="payroll">Payroll</option>
                        <option value="taxes">Tax Records</option>
                        <option value="audit">Audit Trail</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="reportFilters">Filters</label>
                    <textarea id="reportFilters" rows="3" placeholder="Enter custom filters (JSON format)"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('customReportModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="generateCustomReport()">Generate Report</button>
            </div>
        </div>
    </div>

    <!-- Financial Forecasting Modal -->
    <div class="modal-overlay hidden" id="forecastingModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-crystal-ball"></i> Financial Forecasting</h3>
                <button class="modal-close" onclick="hideModal('forecastingModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="forecastPeriod">Forecast Period</label>
                        <select id="forecastPeriod">
                            <option value="3">Next 3 Months</option>
                            <option value="6">Next 6 Months</option>
                            <option value="12">Next 12 Months</option>
                            <option value="24">Next 2 Years</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="forecastModel">Forecasting Model</label>
                        <select id="forecastModel">
                            <option value="linear">Linear Regression</option>
                            <option value="seasonal">Seasonal Analysis</option>
                            <option value="trend">Trend Analysis</option>
                            <option value="advanced">Advanced ML Model</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="forecastMetrics">Metrics to Forecast</label>
                    <select id="forecastMetrics" multiple style="height: 100px;">
                        <option value="revenue">Revenue</option>
                        <option value="expenses">Expenses</option>
                        <option value="profit">Net Profit</option>
                        <option value="cashflow">Cash Flow</option>
                        <option value="payroll">Payroll Costs</option>
                    </select>
                </div>
                <div class="alert info">
                    <span>🔮 AI-powered forecasting uses historical data to predict future financial trends.</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('forecastingModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="generateForecast()">Generate Forecast</button>
            </div>
        </div>
    </div>

    <!-- Security Settings Modal -->
    <div class="modal-overlay hidden" id="securitySettingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-shield-alt"></i> Security Settings</h3>
                <button class="modal-close" onclick="hideModal('securitySettingsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Enable Two-Factor Authentication
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Require Strong Passwords
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Enable Audit Logging
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox"> Enable IP Restrictions
                    </label>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="sessionTimeout">Session Timeout (minutes)</label>
                        <input type="number" id="sessionTimeout" value="30" min="5" max="480">
                    </div>
                    <div class="form-group">
                        <label for="maxLoginAttempts">Max Login Attempts</label>
                        <input type="number" id="maxLoginAttempts" value="5" min="3" max="10">
                    </div>
                </div>
                <div class="form-group">
                    <label for="passwordPolicy">Password Policy</label>
                    <select id="passwordPolicy">
                        <option value="basic">Basic (8+ characters)</option>
                        <option value="medium">Medium (8+ chars, mixed case)</option>
                        <option value="strong">Strong (12+ chars, mixed case, numbers, symbols)</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('securitySettingsModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveSecuritySettings()">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Backup Settings Modal -->
    <div class="modal-overlay hidden" id="backupSettingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-database"></i> Backup & Restore</h3>
                <button class="modal-close" onclick="hideModal('backupSettingsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> Enable Automatic Backups
                    </label>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="backupFrequency">Backup Frequency</label>
                        <select id="backupFrequency">
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="backupRetention">Retention Period (days)</label>
                        <input type="number" id="backupRetention" value="30" min="7" max="365">
                    </div>
                </div>
                <div class="form-group">
                    <label for="backupLocation">Backup Location</label>
                    <select id="backupLocation">
                        <option value="local">Local Storage</option>
                        <option value="cloud">Cloud Storage</option>
                        <option value="both">Both Local & Cloud</option>
                    </select>
                </div>
                <div class="alert warning">
                    <span>⚠️ Regular backups are essential for data protection and disaster recovery.</span>
                </div>
                <div style="margin-top: 20px;">
                    <button type="button" class="btn btn-warning" onclick="createBackup()">
                        <i class="fas fa-save"></i> Create Backup Now
                    </button>
                    <button type="button" class="btn btn-info" onclick="restoreBackup()">
                        <i class="fas fa-upload"></i> Restore from Backup
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('backupSettingsModal')">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveBackupSettings()">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Edit Invoice Modal -->
    <div class="modal-overlay hidden" id="editInvoiceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> Edit Invoice</h3>
                <button class="modal-close" onclick="hideModal('editInvoiceModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editInvoiceForm" class="modal-body" onsubmit="window.invoicesManager.handleEditInvoice(event); return false;">
                <input type="hidden" id="editInvoiceId" name="invoiceId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editInvoiceCustomer">Customer</label>
                        <select id="editInvoiceCustomer" name="customerId" required>
                            <option value="">Select Customer</option>
                            <!-- Options will be populated dynamically from Customer Management -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editInvoiceAmount">Amount</label>
                        <input type="number" id="editInvoiceAmount" name="amount" step="0.01" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editInvoiceDate">Invoice Date</label>
                        <input type="date" id="editInvoiceDate" name="invoiceDate" required>
                    </div>
                    <div class="form-group">
                        <label for="editInvoiceDueDate">Due Date</label>
                        <input type="date" id="editInvoiceDueDate" name="dueDate" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="editInvoiceStatus">Status</label>
                    <select id="editInvoiceStatus" name="status" required>
                        <option value="pending">Pending</option>
                        <option value="paid">Paid</option>
                        <option value="overdue">Overdue</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editInvoiceNotes">Notes</label>
                    <textarea id="editInvoiceNotes" name="notes" rows="3"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('editInvoiceModal')">Cancel</button>
                <button type="submit" form="editInvoiceForm" class="btn btn-primary">Update Invoice</button>
            </div>
        </div>
    </div>

    <!-- Create Customer Modal -->
    <div class="modal-overlay hidden" id="createCustomerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> Add New Customer</h3>
                <button class="modal-close" onclick="hideModal('createCustomerModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createCustomerForm" class="modal-body" onsubmit="window.invoicesManager.handleCreateCustomer(event); return false;">
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerName">Customer Name</label>
                        <input type="text" id="customerName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="customerEmail">Email</label>
                        <input type="email" id="customerEmail" name="email" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerPhone">Phone</label>
                        <input type="tel" id="customerPhone" name="phone">
                    </div>
                </div>
                <div class="form-group">
                    <label for="customerAddress">Address</label>
                    <textarea id="customerAddress" name="address" rows="3"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('createCustomerModal')">Cancel</button>
                <button type="submit" form="createCustomerForm" class="btn btn-primary">Add Customer</button>
            </div>
        </div>
    </div>

    <!-- Edit Customer Modal -->
    <div class="modal-overlay hidden" id="editCustomerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> Edit Customer</h3>
                <button class="modal-close" onclick="hideModal('editCustomerModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editCustomerForm" class="modal-body" onsubmit="window.invoicesManager.handleEditCustomer(event); return false;">
                <input type="hidden" id="editCustomerId" name="customerId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCustomerName">Customer Name</label>
                        <input type="text" id="editCustomerName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="editCustomerEmail">Email</label>
                        <input type="email" id="editCustomerEmail" name="email" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCustomerPhone">Phone</label>
                        <input type="tel" id="editCustomerPhone" name="phone">
                    </div>
                </div>
                <div class="form-group">
                    <label for="editCustomerAddress">Address</label>
                    <textarea id="editCustomerAddress" name="address" rows="3"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('editCustomerModal')">Cancel</button>
                <button type="submit" form="editCustomerForm" class="btn btn-primary">Update Customer</button>
            </div>
        </div>
    </div>

    <!-- Data Management Modal -->
    <div class="modal-overlay hidden" id="dataManagementModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-database"></i> Data Management</h3>
                <button class="modal-close" onclick="hideModal('dataManagementModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert info">
                    <span>⚠️ Use these tools carefully. Data operations cannot be undone.</span>
                </div>

                <!-- Data Statistics -->
                <div class="form-group">
                    <h4>📊 Current Data Statistics</h4>
                    <div id="dataStats" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                            <div><strong>Invoices:</strong> <span id="statsInvoices">0</span></div>
                            <div><strong>Customers:</strong> <span id="statsCustomers">0</span></div>
                            <div><strong>Paid Invoices:</strong> <span id="statsPaid">0</span></div>
                            <div><strong>Pending:</strong> <span id="statsPending">0</span></div>
                            <div><strong>Total Revenue:</strong> $<span id="statsRevenue">0</span></div>
                            <div><strong>Pending Amount:</strong> $<span id="statsPendingAmount">0</span></div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="refreshDataStats()">
                        <i class="fas fa-sync"></i> Refresh Statistics
                    </button>
                </div>

                <!-- Data Export/Import -->
                <div class="form-group">
                    <h4>💾 Data Export & Import</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <button type="button" class="btn btn-primary" onclick="exportAllData()">
                            <i class="fas fa-download"></i> Export All Data
                        </button>
                        <div>
                            <input type="file" id="importFile" accept=".json" style="display: none;" onchange="handleDataImport(this)">
                            <button type="button" class="btn btn-primary" onclick="document.getElementById('importFile').click()">
                                <i class="fas fa-upload"></i> Import Data
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Data Clearing -->
                <div class="form-group">
                    <h4>🗑️ Clear Data</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <button type="button" class="btn btn-warning" onclick="clearInvoicesOnly()">
                            <i class="fas fa-file-invoice"></i> Clear All Invoices
                        </button>
                        <button type="button" class="btn btn-warning" onclick="clearCustomersOnly()">
                            <i class="fas fa-users"></i> Clear All Customers
                        </button>
                    </div>
                </div>

                <!-- Soft Delete Management -->
                <div class="form-group">
                    <h4>🗂️ Soft Delete Management</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <button type="button" class="btn btn-info" onclick="viewSoftDeletedInvoices()">
                            <i class="fas fa-file-invoice"></i> View Deleted Invoices
                        </button>
                        <button type="button" class="btn btn-info" onclick="viewSoftDeletedCustomers()">
                            <i class="fas fa-users"></i> View Deleted Customers
                        </button>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr; gap: 15px; margin-bottom: 15px;">
                        <button type="button" class="btn btn-secondary" onclick="showStorageStats()">
                            <i class="fas fa-chart-bar"></i> Storage Statistics
                        </button>
                    </div>
                    <small style="color: #6c757d; display: block; margin-bottom: 10px;">
                        📁 File storage preserves deleted records for recovery
                    </small>
                </div>

                <!-- System Reset -->
                <div class="form-group">
                    <h4>🔄 System Reset</h4>
                    <button type="button" class="btn btn-danger" onclick="resetEntireSystem()" style="width: 100%;">
                        <i class="fas fa-exclamation-triangle"></i> Reset Entire System (Delete Everything)
                    </button>
                    <small style="color: #dc3545; display: block; margin-top: 5px;">
                        ⚠️ This will permanently delete ALL invoices, customers, and data!
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('dataManagementModal')">Close</button>
            </div>
        </div>
    </div>

    <!-- Soft Deleted Invoices Modal -->
    <div class="modal-overlay hidden" id="softDeletedInvoicesModal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3><i class="fas fa-trash-restore"></i> Soft Deleted Invoices</h3>
                <button class="modal-close" onclick="hideModal('softDeletedInvoicesModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert info">
                    <span>📁 These invoices have been deleted from the UI but preserved in file storage for recovery.</span>
                </div>

                <div id="softDeletedInvoicesContainer">
                    <div class="table-container">
                        <table class="data-table" id="softDeletedInvoicesTable">
                            <thead>
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                    <th>Deleted At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="softDeletedInvoicesTableBody">
                                <!-- Soft deleted invoices will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('softDeletedInvoicesModal')">Close</button>
                <button type="button" class="btn btn-primary" onclick="refreshSoftDeletedInvoices()">
                    <i class="fas fa-sync"></i> Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Soft Deleted Customers Modal -->
    <div class="modal-overlay hidden" id="softDeletedCustomersModal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3><i class="fas fa-user-times"></i> Soft Deleted Customers</h3>
                <button class="modal-close" onclick="hideModal('softDeletedCustomersModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert info">
                    <span>👥 These customers have been deleted from the UI but preserved in file storage for recovery.</span>
                </div>

                <div id="softDeletedCustomersContainer">
                    <div class="table-container">
                        <table class="data-table" id="softDeletedCustomersTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Company</th>
                                    <th>Deleted At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="softDeletedCustomersTableBody">
                                <!-- Soft deleted customers will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('softDeletedCustomersModal')">Close</button>
                <button type="button" class="btn btn-primary" onclick="refreshSoftDeletedCustomers()">
                    <i class="fas fa-sync"></i> Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Include JavaScript Modules -->
    <script src="JS/FileStorageManager.js"></script>
    <script src="JS/Dashboard.js"></script>
    <script src="JS/Invoices.js"></script>
    <script src="JS/Expenses.js"></script>
    <script src="JS/Payroll.js"></script>
    <script src="debug-invoice.js"></script>

    <script>
        // Application State
        let currentUser = null;
        let currentPage = 'dashboard';
        let sessionTimeout = null;
        let sessionWarningTimeout = null;
        const SESSION_DURATION = 30 * 60 * 1000; // 30 minutes
        const SESSION_WARNING_TIME = 5 * 60 * 1000; // 5 minutes before expiry

        // Initialize empty data arrays - all data will be created by users
        let users = [];
        let invoices = [];
        let expenses = [];

        // Data Management System
        class DataManager {
            constructor() {
                this.storageKeys = {
                    users: 'accounting_users',
                    invoices: 'accounting_invoices',
                    expenses: 'accounting_expenses'
                };
                this.loadAllData();
            }

            // Load data from localStorage
            loadAllData() {
                users = this.loadData('users') || [];
                invoices = this.loadData('invoices') || [];
                expenses = this.loadData('expenses') || [];

                // Ensure admin user exists for system access
                this.ensureAdminUser();

                console.log('Data loaded:', {
                    users: users.length,
                    invoices: invoices.length,
                    expenses: expenses.length
                });
            }

            // Ensure admin user exists for system access
            ensureAdminUser() {
                const adminExists = users.find(u => u.username === 'admin');
                if (!adminExists) {
                    const adminUser = {
                        id: this.generateId('users'),
                        firstName: 'System',
                        lastName: 'Administrator',
                        username: 'admin',
                        email: '<EMAIL>',
                        role: 'admin',
                        isActive: true,
                        lastLogin: new Date(),
                        createdAt: new Date()
                    };
                    users.push(adminUser);
                    this.saveData('users', users);
                    console.log('Admin user created for system access');
                }
            }

            // Load specific data type
            loadData(type) {
                try {
                    const data = localStorage.getItem(this.storageKeys[type]);
                    return data ? JSON.parse(data) : null;
                } catch (error) {
                    console.error(`Error loading ${type} data:`, error);
                    return null;
                }
            }

            // Save specific data type
            saveData(type, data) {
                try {
                    localStorage.setItem(this.storageKeys[type], JSON.stringify(data));
                    console.log(`${type} data saved:`, data.length, 'items');
                    this.notifyDataChange(type);
                } catch (error) {
                    console.error(`Error saving ${type} data:`, error);
                }
            }

            // Generate unique ID for data type
            generateId(type) {
                const data = this.getCurrentData(type);
                return data.length > 0 ? Math.max(...data.map(item => item.id)) + 1 : 1;
            }

            // Get current data array
            getCurrentData(type) {
                switch(type) {
                    case 'users': return users;
                    case 'invoices': return invoices;
                    case 'expenses': return expenses;
                    default: return [];
                }
            }

            // Add new item
            addItem(type, itemData) {
                const data = this.getCurrentData(type);
                const newItem = {
                    ...itemData,
                    id: this.generateId(type),
                    createdAt: new Date(),
                    updatedAt: new Date()
                };

                data.push(newItem);
                this.saveData(type, data);
                return newItem;
            }

            // Update existing item
            updateItem(type, id, updateData) {
                const data = this.getCurrentData(type);
                const index = data.findIndex(item => item.id === id);

                if (index !== -1) {
                    data[index] = {
                        ...data[index],
                        ...updateData,
                        updatedAt: new Date()
                    };
                    this.saveData(type, data);
                    return data[index];
                }
                return null;
            }

            // Remove item
            removeItem(type, id) {
                const data = this.getCurrentData(type);
                const index = data.findIndex(item => item.id === id);

                if (index !== -1) {
                    const removedItem = data.splice(index, 1)[0];
                    this.saveData(type, data);
                    return removedItem;
                }
                return null;
            }

            // Clear all data of specific type
            clearData(type) {
                const data = this.getCurrentData(type);
                data.length = 0;
                this.saveData(type, data);
            }

            // Clear all application data
            clearAllData() {
                Object.keys(this.storageKeys).forEach(type => {
                    this.clearData(type);
                });
                // Recreate admin user
                this.ensureAdminUser();
            }

            // Notify other components of data changes
            notifyDataChange(type) {
                // Trigger custom events for real-time updates
                const event = new CustomEvent('dataChanged', {
                    detail: { type, data: this.getCurrentData(type) }
                });
                document.dispatchEvent(event);

                // Update specific managers
                switch(type) {
                    case 'invoices':
                        if (window.invoicesManager) {
                            window.invoicesManager.loadData();
                        }
                        break;
                    case 'expenses':
                        if (window.expensesManager) {
                            window.expensesManager.loadData();
                        }
                        break;
                }
            }

            // Export data for backup
            exportData() {
                return {
                    users: users,
                    invoices: invoices,
                    expenses: expenses,
                    exportDate: new Date()
                };
            }

            // Import data from backup
            importData(importedData) {
                if (importedData.users) {
                    users.length = 0;
                    users.push(...importedData.users);
                    this.saveData('users', users);
                }
                if (importedData.invoices) {
                    invoices.length = 0;
                    invoices.push(...importedData.invoices);
                    this.saveData('invoices', invoices);
                }
                if (importedData.expenses) {
                    expenses.length = 0;
                    expenses.push(...importedData.expenses);
                    this.saveData('expenses', expenses);
                }
                console.log('Data imported successfully');
            }
        }

        // Initialize Data Manager
        const dataManager = new DataManager();

        // Session Management Functions
        function saveSession(user) {
            const sessionData = {
                user: user,
                loginTime: new Date().getTime(),
                expiryTime: new Date().getTime() + SESSION_DURATION
            };
            localStorage.setItem('accountingSession', JSON.stringify(sessionData));
            localStorage.setItem('lastActivity', new Date().getTime().toString());
        }

        function loadSession() {
            try {
                const sessionData = localStorage.getItem('accountingSession');
                const lastActivity = localStorage.getItem('lastActivity');

                if (!sessionData || !lastActivity) {
                    return null;
                }

                const session = JSON.parse(sessionData);
                const now = new Date().getTime();
                const timeSinceActivity = now - parseInt(lastActivity);

                // Check if session has expired
                if (now > session.expiryTime || timeSinceActivity > SESSION_DURATION) {
                    clearSession();
                    return null;
                }

                return session;
            } catch (error) {
                console.error('Error loading session:', error);
                clearSession();
                return null;
            }
        }

        function clearSession() {
            localStorage.removeItem('accountingSession');
            localStorage.removeItem('lastActivity');
            clearTimeout(sessionTimeout);
            clearTimeout(sessionWarningTimeout);
        }

        function updateActivity() {
            localStorage.setItem('lastActivity', new Date().getTime().toString());
            resetSessionTimeout();
        }

        function resetSessionTimeout() {
            clearTimeout(sessionTimeout);
            clearTimeout(sessionWarningTimeout);

            // Set warning timeout (5 minutes before expiry)
            sessionWarningTimeout = setTimeout(() => {
                showSessionWarning();
            }, SESSION_DURATION - SESSION_WARNING_TIME);

            // Set logout timeout
            sessionTimeout = setTimeout(() => {
                autoLogout();
            }, SESSION_DURATION);
        }

        function showSessionWarning() {
            if (confirm('Your session will expire in 5 minutes. Click OK to extend your session.')) {
                updateActivity();
                showAlert('Session extended successfully.', 'success');
            }
        }

        function autoLogout() {
            showAlert('Session expired. Please log in again.', 'warning');
            logout();
        }

        // Authentication Functions
        function handleLogin(e) {
            e.preventDefault();

            const credential = document.getElementById('credential').value;
            const password = document.getElementById('password').value;

            // Simple authentication (in real app, this would be server-side)
            if (credential === 'admin' && password === 'admin') {
                currentUser = users.find(u => u.username === 'admin');
                saveSession(currentUser);
                showAlert('Login successful! Welcome Administrator.', 'success');

                setTimeout(() => {
                    document.getElementById('loginPage').classList.add('hidden');
                    document.getElementById('dashboardPage').classList.remove('hidden');
                    updateUserInfo();
                    applyRoleRestrictions();
                    showPage('dashboard');
                    resetSessionTimeout();
                }, 1500);
            } else {
                // Check other users
                const user = users.find(u =>
                    (u.username === credential || u.email === credential) &&
                    password === 'demo' // Simple demo password for all users
                );

                if (user) {
                    currentUser = user;
                    saveSession(currentUser);
                    showAlert(`Login successful! Welcome ${user.firstName}.`, 'success');

                    setTimeout(() => {
                        document.getElementById('loginPage').classList.add('hidden');
                        document.getElementById('dashboardPage').classList.remove('hidden');
                        updateUserInfo();
                        applyRoleRestrictions();
                        showPage('dashboard');
                        resetSessionTimeout();
                    }, 1500);
                } else {
                    showAlert('Invalid credentials. Use admin/admin or any username with password "demo".', 'error');
                }
            }
        }

        function logout() {
            currentUser = null;
            clearSession();
            document.getElementById('dashboardPage').classList.add('hidden');
            document.getElementById('loginPage').classList.remove('hidden');
            showAlert('Logged out successfully.', 'success');

            // Reset form
            document.getElementById('credential').value = 'admin';
            document.getElementById('password').value = 'admin';
        }

        function checkAuthenticationOnLoad() {
            const session = loadSession();
            if (session && session.user) {
                currentUser = session.user;
                document.getElementById('loginPage').classList.add('hidden');
                document.getElementById('dashboardPage').classList.remove('hidden');
                updateUserInfo();
                applyRoleRestrictions();
                showPage('dashboard');
                resetSessionTimeout();
                return true;
            }
            return false;
        }

        function updateUserInfo() {
            if (currentUser) {
                document.getElementById('userName').textContent = `${currentUser.firstName} ${currentUser.lastName}`;
                document.getElementById('userRole').textContent = currentUser.role;
            }
        }

        function applyRoleRestrictions() {
            const adminOnlyElements = document.querySelectorAll('.admin-only');

            if (currentUser && currentUser.role !== 'admin') {
                adminOnlyElements.forEach(element => {
                    element.style.display = 'none';
                });
            } else {
                adminOnlyElements.forEach(element => {
                    element.style.display = '';
                });
            }
        }

        // Page Navigation
        function showPage(pageName) {
            currentPage = pageName;
            document.getElementById('pageTitle').textContent = pageName.charAt(0).toUpperCase() + pageName.slice(1);

            // Update active menu item
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeMenuItem = document.querySelector(`[onclick="showPage('${pageName}')"]`);
            if (activeMenuItem) {
                activeMenuItem.closest('.menu-item').classList.add('active');
            }

            // Load page content
            loadPageContent(pageName);
        }

        function loadPageContent(pageName) {
            const pageContent = document.getElementById('pageContent');

            switch (pageName) {
                case 'dashboard':
                    pageContent.innerHTML = getDashboardContent();
                    // Initialize dashboard manager
                    if (window.dashboardManager) {
                        window.dashboardManager.init();
                    }
                    break;
                case 'invoices':
                    pageContent.innerHTML = getInvoicesContent();
                    // Force load invoices immediately
                    setTimeout(() => {
                        if (window.invoicesManager) {
                            console.log('=== FORCING INVOICE TABLE LOAD ===');
                            window.invoicesManager.loadInvoicesTable();
                            window.invoicesManager.updateCustomerDropdowns();
                        }
                    }, 200);
                    break;
                case 'customers':
                    pageContent.innerHTML = getCustomersContent();
                    // Initialize customers table
                    if (window.invoicesManager) {
                        window.invoicesManager.loadCustomersTable();
                    }
                    break;
                case 'expenses':
                    pageContent.innerHTML = getExpensesContent();
                    // Initialize expenses manager
                    if (window.expensesManager) {
                        window.expensesManager.init();
                    }
                    break;
                case 'users':
                    if (currentUser && currentUser.role === 'admin') {
                        pageContent.innerHTML = getUsersContent();
                        loadUsersTable();
                    } else {
                        pageContent.innerHTML = '<div class="alert error">Access Denied: Admin privileges required.</div>';
                    }
                    break;
                case 'payroll':
                    pageContent.innerHTML = getPayrollContent();
                    // Initialize payroll manager
                    if (window.payrollManager) {
                        window.payrollManager.init();
                    }
                    break;

                case 'reports':
                    pageContent.innerHTML = getReportsContent();
                    loadReportsCharts();
                    break;
                case 'settings':
                    if (currentUser && currentUser.role === 'admin') {
                        pageContent.innerHTML = getSettingsContent();
                    } else {
                        pageContent.innerHTML = '<div class="alert error">Access Denied: Admin privileges required.</div>';
                    }
                    break;
                default:
                    pageContent.innerHTML = '<div class="alert info">Page under development.</div>';
            }
        }

        // Page Content Functions
        function getDashboardContent() {
            const roleMessage = currentUser.role !== 'admin' ?
                `<div class="alert warning mb-4">
                    <span>⚠️ ${getRoleDescription(currentUser.role)}</span>
                </div>` : '';

            return `
                ${roleMessage}
                <!-- Key Metrics Cards -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$125,000</h3>
                            <p class="metric-label">Total Revenue</p>
                            <span class="metric-change positive">+12.5%</span>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon warning">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$85,000</h3>
                            <p class="metric-label">Total Expenses</p>
                            <span class="metric-change negative">+8.2%</span>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon info">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$40,000</h3>
                            <p class="metric-label">Net Profit</p>
                            <span class="metric-change positive">+15.3%</span>
                        </div>
                    </div>

                    <div class="metric-card" data-metric="pendingRequests">
                        <div class="metric-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value" id="pendingRequestsCount">0</h3>
                            <p class="metric-label">Pending Requests</p>
                            <span class="metric-change" id="pendingRequestsAmount">$0</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>Quick Actions</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('createInvoiceModal')">
                            <i class="fas fa-plus"></i>
                            <span>Create Invoice</span>
                        </button>
                        <button class="action-btn" onclick="showModal('createCustomerModal')">
                            <i class="fas fa-user-plus"></i>
                            <span>Add Customer</span>
                        </button>
                        <button class="action-btn" onclick="showModal('createExpenseModal')">
                            <i class="fas fa-receipt"></i>
                            <span>Add Expense</span>
                        </button>
                        <button class="action-btn ${currentUser.role !== 'admin' ? 'hidden' : ''}" onclick="showModal('createUserModal')">
                            <i class="fas fa-users-cog"></i>
                            <span>Manage Users</span>
                        </button>
                        <button class="action-btn" onclick="showPage('reports')">
                            <i class="fas fa-chart-bar"></i>
                            <span>Generate Report</span>
                        </button>
                        <button class="action-btn" onclick="debugInvoiceSystem()" style="background-color: #ff6b6b;">
                            <i class="fas fa-bug"></i>
                            <span>Debug Invoice System</span>
                        </button>
                        <button class="action-btn" onclick="resetInvoiceData()" style="background-color: #ffa500;">
                            <i class="fas fa-refresh"></i>
                            <span>Reset Invoice Data</span>
                        </button>
                        <button class="action-btn" onclick="forceLoadInvoices()" style="background-color: #28a745;">
                            <i class="fas fa-table"></i>
                            <span>Force Load Invoices</span>
                        </button>
                        <button class="action-btn" onclick="initializeInvoicesManager()" style="background-color: #6f42c1;">
                            <i class="fas fa-sync"></i>
                            <span>Force Init Manager</span>
                        </button>
                        <button class="action-btn" onclick="runComprehensiveDiagnostic()" style="background-color: #17a2b8;">
                            <i class="fas fa-stethoscope"></i>
                            <span>Full Diagnostic</span>
                        </button>
                        <button class="action-btn" onclick="simpleInvoiceTest()" style="background-color: #dc3545;">
                            <i class="fas fa-bolt"></i>
                            <span>SIMPLE TEST</span>
                        </button>
                        <button class="action-btn" onclick="emergencyLoadInvoices()" style="background-color: #e74c3c; color: white; font-weight: bold;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>EMERGENCY LOAD</span>
                        </button>
                    </div>
                </div>

                <!-- Financial Overview Charts -->
                <div class="quick-actions">
                    <h3>Financial Overview</h3>
                    <div class="charts-grid">
                        <div class="chart-container">
                            <h4>Expense Breakdown - Current Month</h4>
                            <canvas id="expensePieChart" width="400" height="300"></canvas>
                        </div>
                        <div class="chart-container">
                            <h4>Net Profit by Month</h4>
                            <canvas id="netProfitChart" width="400" height="300"></canvas>
                        </div>
                        <div class="chart-container">
                            <h4>Total Expenses by Month</h4>
                            <canvas id="totalExpensesChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
            `;
        }

        function getInvoicesContent() {
            return `
                <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>Invoice Management</h3>
                    <button class="btn btn-primary" onclick="showModal('createInvoiceModal')">
                        <i class="fas fa-plus"></i> Create Invoice
                    </button>
                </div>

                <div class="table-container">
                    <table class="data-table" id="invoicesTable">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Due Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="invoicesTableBody">
                            <!-- Invoices will be loaded here -->
                        </tbody>
                    </table>
                </div>
            `;
        }

        function getCustomersContent() {
            return `
                <div class="alert info mb-4">
                    <span>👥 Customer Relationship Management</span>
                </div>

                <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>Customer Management</h3>
                    <button class="btn btn-primary" onclick="showModal('createCustomerModal')">
                        <i class="fas fa-user-plus"></i> Add Customer
                    </button>
                </div>

                <div class="table-container">
                    <table class="data-table" id="customersTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Address</th>
                                <th>Invoices</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <!-- Customer data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            `;
        }

        function getExpensesContent() {
            return `
                <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>Expense Management</h3>
                    <button class="btn btn-primary" onclick="showModal('createExpenseModal')">
                        <i class="fas fa-plus"></i> Add Expense
                    </button>
                </div>

                <div class="table-container">
                    <table class="data-table" id="expensesTable">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Category</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="expensesTableBody">
                            <!-- Expenses will be loaded here -->
                        </tbody>
                    </table>
                </div>
            `;
        }

        function getUsersContent() {
            return `
                <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>User Management</h3>
                    <button class="btn btn-primary" onclick="showModal('createUserModal')">
                        <i class="fas fa-user-plus"></i> Create User
                    </button>
                </div>

                <div class="alert info mb-4">
                    <span>👑 Administrator Access: You have full user management privileges</span>
                </div>

                <div class="table-container">
                    <table class="data-table" id="usersTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- Users will be loaded here -->
                        </tbody>
                    </table>
                </div>
            `;
        }

        function getPayrollContent() {
            return `
                <div class="alert info mb-4">
                    <span>💼 Comprehensive Payroll Management System</span>
                </div>

                <!-- Payroll Metrics -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon success">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">45</h3>
                            <p class="metric-label">Active Employees</p>
                            <span class="metric-change positive">+3 this month</span>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon warning">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$125,450</h3>
                            <p class="metric-label">Monthly Payroll</p>
                            <span class="metric-change positive">+5.2%</span>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon info">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$18,750</h3>
                            <p class="metric-label">Tax Deductions</p>
                            <span class="metric-change neutral">15% of gross</span>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon primary">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="metric-info">
                            <h3 class="metric-value">$106,700</h3>
                            <p class="metric-label">Net Payroll</p>
                            <span class="metric-change positive">Ready for processing</span>
                        </div>
                    </div>
                </div>

                <!-- Payroll Actions -->
                <div class="quick-actions">
                    <h3>Payroll Management</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('calculatePayrollModal')">
                            <i class="fas fa-calculator"></i>
                            <span>Calculate Payroll</span>
                        </button>
                        <button class="action-btn" onclick="showModal('generatePayslipsModal')">
                            <i class="fas fa-file-pdf"></i>
                            <span>Generate Payslips</span>
                        </button>
                        <button class="action-btn" onclick="showModal('payrollReportsModal')">
                            <i class="fas fa-chart-pie"></i>
                            <span>Payroll Reports</span>
                        </button>
                        <button class="action-btn" onclick="showModal('employeeManagementModal')">
                            <i class="fas fa-user-plus"></i>
                            <span>Manage Employees</span>
                        </button>
                        <button class="action-btn" onclick="showModal('taxSettingsModal')">
                            <i class="fas fa-cogs"></i>
                            <span>Tax Settings</span>
                        </button>
                        <button class="action-btn" onclick="showModal('payrollHistoryModal')">
                            <i class="fas fa-history"></i>
                            <span>Payroll History</span>
                        </button>
                    </div>
                </div>

                <!-- Employee Payroll Table -->
                <div class="table-container">
                    <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
                        <h3>Current Pay Period</h3>
                        <div>
                            <button class="btn btn-secondary" onclick="exportPayrollData()">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <button class="btn btn-primary" onclick="processPayroll()">
                                <i class="fas fa-play"></i> Process Payroll
                            </button>
                        </div>
                    </div>
                    <table class="data-table" id="payrollTable">
                        <thead>
                            <tr>
                                <th>Employee</th>
                                <th>Position</th>
                                <th>Basic Salary</th>
                                <th>Allowances</th>
                                <th>Overtime</th>
                                <th>Gross Pay</th>
                                <th>Tax</th>
                                <th>Deductions</th>
                                <th>Net Pay</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="payrollTableBody">
                            <!-- Payroll data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            `;
        }



        function getReportsContent() {
            return `
                <div class="alert info mb-4">
                    <span>📊 Financial Reports & Analytics</span>
                </div>

                <!-- Financial Charts -->
                <div class="quick-actions">
                    <h3>Financial Analytics</h3>
                    <div class="charts-grid">
                        <div class="chart-container">
                            <h4>Expense Breakdown - Current Month</h4>
                            <canvas id="reportsExpensePieChart" width="400" height="300"></canvas>
                        </div>
                        <div class="chart-container">
                            <h4>Net Profit by Month</h4>
                            <canvas id="reportsNetProfitChart" width="400" height="300"></canvas>
                        </div>
                        <div class="chart-container">
                            <h4>Total Expenses by Month</h4>
                            <canvas id="reportsTotalExpensesChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
            `;
        }

        function getSettingsContent() {
            return `
                <div class="alert success mb-4">
                    <span>⚙️ Enterprise System Configuration & Management</span>
                </div>

                <div class="quick-actions">
                    <h3>Company Configuration</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('companySettingsModal')">
                            <i class="fas fa-building"></i>
                            <span>Company Profile</span>
                        </button>
                        <button class="action-btn" onclick="showModal('fiscalYearModal')">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Fiscal Year Settings</span>
                        </button>
                        <button class="action-btn" onclick="showModal('currencySettingsModal')">
                            <i class="fas fa-dollar-sign"></i>
                            <span>Currency & Localization</span>
                        </button>
                        <button class="action-btn" onclick="showModal('chartOfAccountsModal')">
                            <i class="fas fa-list"></i>
                            <span>Chart of Accounts</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Security & Access Control</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('securitySettingsModal')">
                            <i class="fas fa-shield-alt"></i>
                            <span>Security Policies</span>
                        </button>
                        <button class="action-btn" onclick="showModal('userPermissionsModal')">
                            <i class="fas fa-user-shield"></i>
                            <span>User Permissions</span>
                        </button>
                        <button class="action-btn" onclick="showModal('auditSettingsModal')">
                            <i class="fas fa-search"></i>
                            <span>Audit Configuration</span>
                        </button>
                        <button class="action-btn" onclick="showModal('mfaSettingsModal')">
                            <i class="fas fa-mobile-alt"></i>
                            <span>Multi-Factor Auth</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Tax & Compliance</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('taxConfigModal')">
                            <i class="fas fa-percentage"></i>
                            <span>Tax Configuration</span>
                        </button>
                        <button class="action-btn" onclick="showModal('complianceSettingsModal')">
                            <i class="fas fa-gavel"></i>
                            <span>Compliance Rules</span>
                        </button>
                        <button class="action-btn" onclick="showModal('reportingStandardsModal')">
                            <i class="fas fa-file-contract"></i>
                            <span>Reporting Standards</span>
                        </button>
                        <button class="action-btn" onclick="showModal('regulatorySettingsModal')">
                            <i class="fas fa-balance-scale"></i>
                            <span>Regulatory Settings</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>System Management</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('backupSettingsModal')">
                            <i class="fas fa-database"></i>
                            <span>Backup & Restore</span>
                        </button>
                        <button class="action-btn" onclick="showModal('dataManagementModal')">
                            <i class="fas fa-cogs"></i>
                            <span>Data Management</span>
                        </button>
                        <button class="action-btn" onclick="showModal('integrationSettingsModal')">
                            <i class="fas fa-plug"></i>
                            <span>API & Integrations</span>
                        </button>
                        <button class="action-btn" onclick="showModal('notificationSettingsModal')">
                            <i class="fas fa-bell"></i>
                            <span>Notifications</span>
                        </button>
                        <button class="action-btn" onclick="showModal('systemPreferencesModal')">
                            <i class="fas fa-cogs"></i>
                            <span>System Preferences</span>
                        </button>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Advanced Features</h3>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="showModal('workflowSettingsModal')">
                            <i class="fas fa-project-diagram"></i>
                            <span>Workflow Automation</span>
                        </button>
                        <button class="action-btn" onclick="showModal('templateManagementModal')">
                            <i class="fas fa-file-alt"></i>
                            <span>Document Templates</span>
                        </button>
                        <button class="action-btn" onclick="showModal('customFieldsModal')">
                            <i class="fas fa-plus-square"></i>
                            <span>Custom Fields</span>
                        </button>
                        <button class="action-btn" onclick="showModal('systemHealthModal')">
                            <i class="fas fa-heartbeat"></i>
                            <span>System Health</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // Data Loading Functions - Removed conflicting invoice functions (now handled by JS modules)

        function loadExpensesTable() {
            const tbody = document.getElementById('expensesTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';
            expenses.forEach(expense => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${formatDate(expense.date)}</td>
                    <td>$${expense.amount.toLocaleString()}</td>
                    <td>${expense.category.replace('-', ' ')}</td>
                    <td>${expense.description}</td>
                    <td><span class="status-badge ${expense.status}">${expense.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-secondary" onclick="editExpense(${expense.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${currentUser.role === 'admin' || currentUser.role === 'manager' ?
                            `<button class="btn btn-sm btn-success" onclick="approveExpense(${expense.id})">
                                <i class="fas fa-check"></i>
                            </button>` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function loadUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.firstName} ${user.lastName}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td><span class="status-badge ${user.role}">${user.role}</span></td>
                    <td><span class="status-badge ${user.isActive ? 'success' : 'error'}">${user.isActive ? 'Active' : 'Inactive'}</span></td>
                    <td>${formatDateTime(user.lastLogin)}</td>
                    <td>
                        <button class="btn btn-sm btn-secondary" onclick="editUser(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${user.id !== currentUser.id ?
                            `<button class="btn btn-sm btn-danger" onclick="deactivateUser(${user.id})">
                                <i class="fas fa-ban"></i>
                            </button>` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Chart Functions
        function loadDashboardCharts() {
            setTimeout(() => {
                loadExpensePieChart();
                loadNetProfitChart();
                loadTotalExpensesChart();
            }, 100);
        }

        function loadExpensePieChart() {
            const canvas = document.getElementById('expensePieChart');
            if (!canvas) return;

            const currentMonth = new Date().toLocaleString('default', { month: 'long' });
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['Office Supplies', 'Travel', 'Marketing', 'Utilities', 'Software', 'Other'],
                    datasets: [{
                        data: [850, 420, 1200, 380, 250, 450],
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF',
                            '#FF9F40'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentMonth} ${new Date().getFullYear()}`,
                            font: {
                                size: 14,
                                weight: 'normal'
                            },
                            color: '#6b7280',
                            padding: {
                                bottom: 20
                            }
                        },
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${context.label}: $${value.toLocaleString()} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function loadNetProfitChart() {
            const canvas = document.getElementById('netProfitChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Net Profit',
                        data: [4000, 6000, 7000, 4000, 7000, 7000, 8500, 9200, 8800, 10500, 11200, 12000],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true,
                        borderWidth: 3,
                        pointBackgroundColor: '#10b981',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function loadTotalExpensesChart() {
            const canvas = document.getElementById('totalExpensesChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Total Expenses',
                        data: [8000, 9000, 11000, 10000, 13000, 15000, 14500, 16200, 15800, 17500, 18200, 19000],
                        backgroundColor: '#f59e0b',
                        borderColor: '#d97706',
                        borderWidth: 1,
                        borderRadius: 4,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function loadReportsCharts() {
            setTimeout(() => {
                loadReportsExpensePieChart();
                loadReportsNetProfitChart();
                loadReportsTotalExpensesChart();
            }, 100);
        }

        function loadReportsExpensePieChart() {
            const canvas = document.getElementById('reportsExpensePieChart');
            if (!canvas) return;

            const currentMonth = new Date().toLocaleString('default', { month: 'long' });
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['Office Supplies', 'Travel', 'Marketing', 'Utilities', 'Software', 'Other'],
                    datasets: [{
                        data: [850, 420, 1200, 380, 250, 450],
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF',
                            '#FF9F40'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentMonth} ${new Date().getFullYear()}`,
                            font: {
                                size: 14,
                                weight: 'normal'
                            },
                            color: '#6b7280',
                            padding: {
                                bottom: 20
                            }
                        },
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${context.label}: $${value.toLocaleString()} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function loadReportsNetProfitChart() {
            const canvas = document.getElementById('reportsNetProfitChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Net Profit',
                        data: [4000, 6000, 7000, 4000, 7000, 7000, 8500, 9200, 8800, 10500, 11200, 12000],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true,
                        borderWidth: 3,
                        pointBackgroundColor: '#10b981',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function loadReportsTotalExpensesChart() {
            const canvas = document.getElementById('reportsTotalExpensesChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Total Expenses',
                        data: [8000, 9000, 11000, 10000, 13000, 15000, 14500, 16200, 15800, 17500, 18200, 19000],
                        backgroundColor: '#f59e0b',
                        borderColor: '#d97706',
                        borderWidth: 1,
                        borderRadius: 4,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Modal Functions (Basic - Enhanced versions are defined later)

        // Form Handlers
        function handleCreateUser(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const userData = {
                id: users.length + 1,
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                username: formData.get('username'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                role: formData.get('role'),
                isActive: true,
                lastLogin: null
            };

            // Validate passwords match
            if (formData.get('password') !== formData.get('confirmPassword')) {
                showAlert('Passwords do not match', 'error');
                return;
            }

            // Check if username/email already exists
            if (users.some(u => u.username === userData.username || u.email === userData.email)) {
                showAlert('Username or email already exists', 'error');
                return;
            }

            users.push(userData);
            hideModal('createUserModal');
            showAlert('User created successfully', 'success');

            if (currentPage === 'users') {
                loadUsersTable();
            }
        }

        // Invoice creation now handled by JS/Invoices.js module

        function handleCreateExpense(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const expenseData = {
                date: formData.get('expenseDate'),
                amount: parseFloat(formData.get('amount')),
                category: formData.get('category'),
                description: formData.get('description')
            };

            // Use ExpensesManager if available
            if (window.expensesManager) {
                window.expensesManager.createExpense(expenseData);
                hideModal('createExpenseModal');
                showAlert('Expense added successfully', 'success');
            } else {
                // Fallback to direct array manipulation
                const newExpense = {
                    id: Date.now(),
                    ...expenseData,
                    status: 'pending'
                };
                expenses.push(newExpense);
                hideModal('createExpenseModal');
                showAlert('Expense added successfully', 'success');
            }

            if (currentPage === 'expenses') {
                loadExpensesTable();
            }
        }

        // Utility Functions
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        function formatDateTime(date) {
            if (!date) return 'Never';
            return new Date(date).toLocaleString();
        }

        function getRoleDescription(role) {
            const descriptions = {
                admin: 'Full system access - no restrictions',
                manager: 'Management access - cannot manage users or system settings',
                accountant: 'Accounting access - cannot approve expenses or manage users',
                auditor: 'Read-only access - cannot modify any data',
                employee: 'Limited access - can only view own data'
            };
            return descriptions[role] || 'Unknown role';
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }



        function showAlert(message, type = 'info') {
            const alertElement = document.getElementById('alertMessage');
            if (!alertElement) return;

            const alertText = alertElement.querySelector('.alert-text');
            alertText.textContent = message;

            alertElement.className = `alert ${type}`;
            alertElement.classList.remove('hidden');

            if (type === 'success') {
                setTimeout(() => {
                    alertElement.classList.add('hidden');
                }, 3000);
            }
        }

        function hideAlert() {
            const alertElement = document.getElementById('alertMessage');
            if (alertElement) {
                alertElement.classList.add('hidden');
            }
        }

        // Debug function to test invoice system
        function debugInvoiceSystem() {
            console.log('=== COMPREHENSIVE INVOICE SYSTEM DEBUG ===');

            // Check if InvoicesManager exists
            if (!window.invoicesManager) {
                showAlert('InvoicesManager not found!', 'error');
                console.error('InvoicesManager not found');
                return;
            }

            console.log('InvoicesManager found:', window.invoicesManager);
            console.log('Customers:', window.invoicesManager.customers);
            console.log('Invoices:', window.invoicesManager.invoices);
            console.log('Is initialized:', window.invoicesManager.isInitialized);
            console.log('Current filter:', window.invoicesManager.currentFilter);
            console.log('Current sort:', window.invoicesManager.currentSort);

            // Check localStorage directly
            const savedInvoices = localStorage.getItem('accounting_invoices');
            const savedCustomers = localStorage.getItem('accounting_customers');
            console.log('localStorage invoices:', savedInvoices ? JSON.parse(savedInvoices) : 'null');
            console.log('localStorage customers:', savedCustomers ? JSON.parse(savedCustomers) : 'null');

            // Check if invoices table exists
            const tbody = document.getElementById('invoicesTableBody');
            console.log('Invoices table body found:', !!tbody);
            if (tbody) {
                console.log('Table body content:', tbody.innerHTML);
            }

            // Check current page
            const currentPage = document.querySelector('.page-content').innerHTML;
            console.log('Current page contains invoicesTableBody:', currentPage.includes('invoicesTableBody'));

            // Test getFilteredInvoices
            if (window.invoicesManager.invoices.length > 0) {
                const filtered = window.invoicesManager.getFilteredInvoices();
                console.log('Filtered invoices:', filtered);
                const sorted = window.invoicesManager.getSortedInvoices(filtered);
                console.log('Sorted invoices:', sorted);
            }

            // Force table reload if we're on invoices page
            if (tbody) {
                console.log('Forcing table reload...');
                window.invoicesManager.loadInvoicesTable();
            }

            // Test customer dropdown population
            window.invoicesManager.updateCustomerDropdowns();

            const dropdown = document.getElementById('invoiceCustomer');
            if (dropdown) {
                console.log('Customer dropdown options:', dropdown.options.length);
                for (let i = 0; i < dropdown.options.length; i++) {
                    console.log(`Option ${i}:`, dropdown.options[i].value, dropdown.options[i].text);
                }
            } else {
                console.error('Customer dropdown not found');
            }

            showAlert(`Debug complete. Customers: ${window.invoicesManager.customers.length}, Invoices: ${window.invoicesManager.invoices.length}`, 'info');
        }

        // Reset function to clear localStorage and reinitialize data
        function resetInvoiceData() {
            if (confirm('This will clear all invoice and customer data and reset to sample data. Are you sure?')) {
                // Clear localStorage
                localStorage.removeItem('accounting_invoices');
                localStorage.removeItem('accounting_customers');

                // Reinitialize the invoice manager
                if (window.invoicesManager) {
                    window.invoicesManager.invoices = [];
                    window.invoicesManager.customers = [];
                    window.invoicesManager.nextInvoiceNumber = 1;
                    window.invoicesManager.isInitialized = false;

                    // Reinitialize with sample data
                    window.invoicesManager.init();

                    showAlert('Invoice data has been reset to sample data!', 'success');

                    // Refresh the current page if it's invoices or customers
                    const currentPage = document.querySelector('.menu-link.active')?.onclick?.toString().match(/showPage\('([^']+)'\)/)?.[1];
                    if (currentPage === 'invoices' || currentPage === 'customers') {
                        showPage(currentPage);
                    }
                } else {
                    showAlert('Invoice manager not found!', 'error');
                }
            }
        }

        // Simple test function to check invoice system
        window.testInvoiceSystem = function() {
            console.log('=== SIMPLE INVOICE TEST ===');
            console.log('InvoicesManager exists:', !!window.invoicesManager);
            if (window.invoicesManager) {
                console.log('Invoices:', window.invoicesManager.invoices);
                console.log('Customers:', window.invoicesManager.customers);
                console.log('Initialized:', window.invoicesManager.isInitialized);

                // Force reload table if on invoices page
                const tbody = document.getElementById('invoicesTableBody');
                if (tbody) {
                    console.log('Table body found, forcing reload...');
                    window.invoicesManager.loadInvoicesTable();
                } else {
                    console.log('Table body not found - not on invoices page');
                }
            }
        };

        // Force load invoices with hardcoded data to test table rendering
        window.forceLoadInvoices = function() {
            console.log('=== FORCE LOADING INVOICES ===');

            // First, navigate to invoices page if not already there
            showPage('invoices');

            setTimeout(() => {
                const tbody = document.getElementById('invoicesTableBody');
                if (!tbody) {
                    showAlert('Invoice table not found! Make sure you are on the invoices page.', 'error');
                    return;
                }

                console.log('Table body found, inserting test data...');

                // Clear existing content
                tbody.innerHTML = '';

                // Insert hardcoded test invoices directly
                const testInvoices = [
                    {
                        number: 'INV-001',
                        customerName: 'ABC Corporation',
                        total: 5750,
                        date: '2024-06-01',
                        dueDate: '2024-07-01',
                        status: 'paid',
                        id: 1
                    },
                    {
                        number: 'INV-002',
                        customerName: 'XYZ Ltd',
                        total: 4025,
                        date: '2024-06-05',
                        dueDate: '2024-07-05',
                        status: 'pending',
                        id: 2
                    },
                    {
                        number: 'INV-003',
                        customerName: 'Tech Solutions Inc',
                        total: 8625,
                        date: '2024-06-10',
                        dueDate: '2024-07-10',
                        status: 'overdue',
                        id: 3
                    }
                ];

                testInvoices.forEach(invoice => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${invoice.number}</td>
                        <td>${invoice.customerName}</td>
                        <td>$${invoice.total.toLocaleString()}</td>
                        <td>${new Date(invoice.date).toLocaleDateString()}</td>
                        <td>${new Date(invoice.dueDate).toLocaleDateString()}</td>
                        <td><span class="status-badge ${invoice.status}">${invoice.status}</span></td>
                        <td>
                            <div class="table-actions">
                                <button class="btn btn-sm btn-secondary" title="View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-success" title="Mark as Paid">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                });

                showAlert('Test invoices loaded successfully!', 'success');
                console.log('Test invoices inserted into table');

                // Also try to initialize the invoice manager with this data
                if (window.invoicesManager) {
                    window.invoicesManager.invoices = testInvoices.map(inv => ({
                        ...inv,
                        customerId: inv.id,
                        amount: inv.total - (inv.total * 0.15),
                        subtotal: inv.total - (inv.total * 0.15),
                        tax: inv.total * 0.15,
                        items: [],
                        notes: 'Test invoice',
                        createdAt: new Date(inv.date),
                        updatedAt: new Date(inv.date)
                    }));
                    window.invoicesManager.saveData();
                    console.log('Invoice manager data updated');
                }
            }, 500);
        };

        // Comprehensive diagnostic function
        window.runComprehensiveDiagnostic = function() {
            console.log('=== COMPREHENSIVE DIAGNOSTIC ===');

            let report = [];

            // Step 1: Check if InvoicesManager exists
            report.push('1. InvoicesManager exists: ' + !!window.invoicesManager);

            if (!window.invoicesManager) {
                showAlert('InvoicesManager not found! Check console for details.', 'error');
                console.log(report.join('\n'));
                return;
            }

            // Step 2: Check initialization
            report.push('2. Is initialized: ' + window.invoicesManager.isInitialized);

            // Step 3: Check data arrays
            report.push('3. Invoices count: ' + window.invoicesManager.invoices.length);
            report.push('4. Customers count: ' + window.invoicesManager.customers.length);

            // Step 4: Check localStorage
            const savedInvoices = localStorage.getItem('accounting_invoices');
            const savedCustomers = localStorage.getItem('accounting_customers');
            report.push('5. localStorage invoices: ' + (savedInvoices ? 'exists' : 'null'));
            report.push('6. localStorage customers: ' + (savedCustomers ? 'exists' : 'null'));

            // Step 5: Check current page
            const currentPageContent = document.querySelector('.page-content').innerHTML;
            const hasInvoiceTable = currentPageContent.includes('invoicesTableBody');
            report.push('7. Current page has invoice table: ' + hasInvoiceTable);

            // Step 6: Check table element
            const tbody = document.getElementById('invoicesTableBody');
            report.push('8. Table body element found: ' + !!tbody);

            if (tbody) {
                report.push('9. Table body content length: ' + tbody.innerHTML.length);
            }

            // Step 7: Check filter settings
            report.push('10. Current filter: ' + window.invoicesManager.currentFilter);
            report.push('11. Current sort: ' + window.invoicesManager.currentSort);

            // Step 8: Test filtering
            if (window.invoicesManager.invoices.length > 0) {
                const filtered = window.invoicesManager.getFilteredInvoices();
                report.push('12. Filtered invoices count: ' + filtered.length);
            }

            console.log(report.join('\n'));

            // Show summary alert
            const summary = `Diagnostic Complete:
Invoices: ${window.invoicesManager.invoices.length}
Customers: ${window.invoicesManager.customers.length}
Table Found: ${!!tbody}
Initialized: ${window.invoicesManager.isInitialized}`;

            showAlert(summary, 'info');

            // If we have data but no table display, try to fix it
            if (window.invoicesManager.invoices.length > 0 && tbody && tbody.innerHTML.includes('No invoices found')) {
                console.log('Data exists but table shows no invoices - attempting fix...');
                window.invoicesManager.loadInvoicesTable();
            }
        };

        // SIMPLE INVOICE TEST - Direct approach
        window.simpleInvoiceTest = function() {
            console.log('=== SIMPLE INVOICE TEST ===');

            // Step 1: Go to invoices page
            showPage('invoices');

            // Step 2: Wait and force load
            setTimeout(() => {
                console.log('Step 1: Checking InvoicesManager...');
                if (!window.invoicesManager) {
                    showAlert('❌ InvoicesManager not found!', 'error');
                    return;
                }

                console.log('Step 2: InvoicesManager found, checking data...');
                console.log('Invoices:', window.invoicesManager.invoices.length);
                console.log('Customers:', window.invoicesManager.customers.length);

                console.log('Step 3: Looking for table...');
                const tbody = document.getElementById('invoicesTableBody');
                if (!tbody) {
                    showAlert('❌ Table not found! Make sure you are on invoices page.', 'error');
                    return;
                }

                console.log('Step 4: Table found, forcing load...');
                window.invoicesManager.loadInvoicesTable();

                showAlert('✅ Simple test complete! Check console for details.', 'success');
            }, 1000);
        };

        // EMERGENCY INVOICE LOADER - Direct HTML insertion
        window.emergencyLoadInvoices = function() {
            console.log('=== EMERGENCY INVOICE LOADER ===');

            // Go to invoices page first
            showPage('invoices');

            setTimeout(() => {
                const tbody = document.getElementById('invoicesTableBody');
                if (!tbody) {
                    showAlert('❌ Cannot find invoice table!', 'error');
                    return;
                }

                // Direct HTML insertion - bypassing all JavaScript logic
                tbody.innerHTML = `
                    <tr>
                        <td><strong>INV-001</strong></td>
                        <td>ABC Corporation</td>
                        <td><strong>$5,750</strong></td>
                        <td>6/1/2024</td>
                        <td>7/1/2024</td>
                        <td><span class="status-badge paid">PAID</span></td>
                        <td>
                            <div class="table-actions">
                                <button class="btn btn-sm btn-secondary" title="View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-success" title="Mark as Paid">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>INV-002</strong></td>
                        <td>XYZ Ltd</td>
                        <td><strong>$4,025</strong></td>
                        <td>6/5/2024</td>
                        <td>7/5/2024</td>
                        <td><span class="status-badge pending">PENDING</span></td>
                        <td>
                            <div class="table-actions">
                                <button class="btn btn-sm btn-secondary" title="View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-success" title="Mark as Paid">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>INV-003</strong></td>
                        <td>Tech Solutions Inc</td>
                        <td><strong>$8,625</strong></td>
                        <td>6/10/2024</td>
                        <td>7/10/2024</td>
                        <td><span class="status-badge overdue">OVERDUE</span></td>
                        <td>
                            <div class="table-actions">
                                <button class="btn btn-sm btn-secondary" title="View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-success" title="Mark as Paid">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;

                showAlert('✅ Emergency invoices loaded successfully!', 'success');
                console.log('✅ Emergency invoices inserted directly into table');
            }, 500);
        };

        // Enhanced Action Functions - Invoice functions now handled by JS/Invoices.js module

        function editExpense(id) {
            showAlert('Opening expense editor with receipt management...', 'info');
            updateActivity();
        }

        function approveExpense(id) {
            showAlert('Expense approved successfully. Notification sent to employee.', 'success');
            updateActivity();
        }

        function editUser(id) {
            showAlert('Opening user profile with advanced permissions...', 'info');
            updateActivity();
        }

        function deactivateUser(id) {
            if (confirm('Are you sure you want to deactivate this user?')) {
                showAlert('User deactivated successfully. Access revoked immediately.', 'success');
                updateActivity();
            }
        }

        // Advanced Payroll Functions
        function calculatePayroll() {
            showAlert('Calculating payroll with tax deductions and benefits...', 'info');
            setTimeout(() => {
                showAlert('Payroll calculated successfully! Review before processing.', 'success');
                hideModal('calculatePayrollModal');
            }, 2000);
            updateActivity();
        }

        function saveEmployee() {
            const firstName = document.getElementById('empFirstName').value;
            const lastName = document.getElementById('empLastName').value;

            if (firstName && lastName) {
                showAlert(`Employee ${firstName} ${lastName} added successfully!`, 'success');
                hideModal('employeeManagementModal');
            } else {
                showAlert('Please fill in all required fields.', 'error');
            }
            updateActivity();
        }

        // Advanced Settings Functions
        function saveCompanySettings() {
            showAlert('Company settings saved successfully!', 'success');
            hideModal('companySettingsModal');
            updateActivity();
        }

        function saveTaxConfiguration() {
            showAlert('Tax configuration updated successfully!', 'success');
            hideModal('taxConfigModal');
            updateActivity();
        }

        function generateCustomReport() {
            const reportName = document.getElementById('reportName').value;
            if (reportName) {
                showAlert(`Generating custom report: ${reportName}...`, 'info');
                setTimeout(() => {
                    showAlert('Custom report generated successfully!', 'success');
                    hideModal('customReportModal');
                }, 2000);
            } else {
                showAlert('Please enter a report name.', 'error');
            }
            updateActivity();
        }

        // Advanced Financial Functions
        function loadPayrollTable() {
            const tbody = document.getElementById('payrollTableBody');
            if (!tbody) return;

            const payrollData = [
                { id: 1, name: 'John Smith', position: 'Senior Accountant', basic: 5000, allowances: 500, overtime: 200, tax: 850, deductions: 100, status: 'pending' },
                { id: 2, name: 'Jane Doe', position: 'Financial Analyst', basic: 4500, allowances: 400, overtime: 150, tax: 765, deductions: 50, status: 'approved' },
                { id: 3, name: 'Mike Johnson', position: 'Accounts Manager', basic: 6000, allowances: 600, overtime: 300, tax: 1035, deductions: 150, status: 'pending' },
                { id: 4, name: 'Sarah Wilson', position: 'Junior Accountant', basic: 3500, allowances: 300, overtime: 100, tax: 585, deductions: 75, status: 'draft' },
                { id: 5, name: 'David Brown', position: 'Payroll Specialist', basic: 4000, allowances: 350, overtime: 120, tax: 670, deductions: 80, status: 'approved' }
            ];

            tbody.innerHTML = '';
            payrollData.forEach(emp => {
                const gross = emp.basic + emp.allowances + emp.overtime;
                const net = gross - emp.tax - emp.deductions;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${emp.name}</td>
                    <td>${emp.position}</td>
                    <td>$${emp.basic.toLocaleString()}</td>
                    <td>$${emp.allowances.toLocaleString()}</td>
                    <td>$${emp.overtime.toLocaleString()}</td>
                    <td>$${gross.toLocaleString()}</td>
                    <td>$${emp.tax.toLocaleString()}</td>
                    <td>$${emp.deductions.toLocaleString()}</td>
                    <td>$${net.toLocaleString()}</td>
                    <td><span class="status-badge ${emp.status}">${emp.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-secondary" onclick="editPayroll(${emp.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="approvePayroll(${emp.id})">
                            <i class="fas fa-check"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function editPayroll(id) {
            showAlert('Opening payroll editor with detailed breakdown...', 'info');
            updateActivity();
        }

        function approvePayroll(id) {
            showAlert('Payroll entry approved successfully!', 'success');
            updateActivity();
        }

        // Advanced Modal Functions
        function generateForecast() {
            const period = document.getElementById('forecastPeriod').value;
            const model = document.getElementById('forecastModel').value;

            showAlert(`Generating ${period}-month forecast using ${model} model...`, 'info');
            setTimeout(() => {
                showAlert('Financial forecast generated successfully! Check reports section.', 'success');
                hideModal('forecastingModal');
            }, 3000);
            updateActivity();
        }

        function saveSecuritySettings() {
            showAlert('Security settings updated successfully!', 'success');
            hideModal('securitySettingsModal');
            updateActivity();
        }

        function saveBackupSettings() {
            showAlert('Backup settings saved successfully!', 'success');
            hideModal('backupSettingsModal');
            updateActivity();
        }

        function createBackup() {
            showAlert('Creating system backup...', 'info');
            setTimeout(() => {
                showAlert('Backup created successfully! Stored securely.', 'success');
            }, 2000);
            updateActivity();
        }

        function restoreBackup() {
            if (confirm('Are you sure you want to restore from backup? This will overwrite current data.')) {
                showAlert('Restoring from backup...', 'info');
                setTimeout(() => {
                    showAlert('System restored successfully from backup!', 'success');
                }, 3000);
            }
            updateActivity();
        }

        // Enhanced Dashboard with Real-time Updates
        function updateDashboardMetrics() {
            // Simulate real-time data updates
            const metrics = [
                { id: 'totalRevenue', value: '$' + (125000 + Math.floor(Math.random() * 5000)).toLocaleString() },
                { id: 'totalExpenses', value: '$' + (85000 + Math.floor(Math.random() * 3000)).toLocaleString() },
                { id: 'netProfit', value: '$' + (40000 + Math.floor(Math.random() * 2000)).toLocaleString() },
                { id: 'pendingInvoices', value: (12 + Math.floor(Math.random() * 5)).toString() }
            ];

            metrics.forEach(metric => {
                const element = document.querySelector(`[data-metric="${metric.id}"]`);
                if (element) {
                    element.textContent = metric.value;
                }
            });
        }

        // Auto-refresh dashboard every 5 minutes
        function startDashboardAutoRefresh() {
            setInterval(() => {
                if (currentPage === 'dashboard' && currentUser) {
                    updateDashboardMetrics();
                    console.log('Dashboard metrics updated automatically');
                }
            }, 5 * 60 * 1000); // 5 minutes
        }

        // Enhanced Error Handling and Logging
        function logUserAction(action, details = {}) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                user: currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'Anonymous',
                action: action,
                details: details,
                page: currentPage
            };

            console.log('User Action:', logEntry);
            // In production, this would send to audit logging service
        }

        // Enhanced Modal Management
        function showModal(modalId) {
            console.log('Opening modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');

                // Set default dates for forms
                if (modalId === 'createInvoiceModal') {
                    console.log('Setting up invoice modal...');

                    // Set default dates
                    const today = new Date().toISOString().split('T')[0];
                    const invoiceDateField = document.getElementById('invoiceDate');
                    const dueDateField = document.getElementById('dueDate');

                    if (invoiceDateField) {
                        invoiceDateField.value = today;
                    }

                    if (dueDateField) {
                        const dueDate = new Date();
                        dueDate.setDate(dueDate.getDate() + 30);
                        dueDateField.value = dueDate.toISOString().split('T')[0];
                    }

                    // Populate customer dropdown
                    if (window.invoicesManager) {
                        console.log('Updating customer dropdowns...');
                        window.invoicesManager.updateCustomerDropdowns();

                        // Ensure form handler is attached
                        const form = document.getElementById('createInvoiceForm');
                        if (form) {
                            // Remove any existing listeners
                            const newForm = form.cloneNode(true);
                            form.parentNode.replaceChild(newForm, form);

                            // Add fresh event listener
                            newForm.addEventListener('submit', function(e) {
                                e.preventDefault();
                                console.log('Form submitted via event listener');
                                window.invoicesManager.handleCreateInvoice(e);
                            });
                        }
                    }
                } else if (modalId === 'createExpenseModal') {
                    document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
                } else if (modalId === 'dataManagementModal') {
                    // Refresh data statistics when opening data management modal
                    setTimeout(refreshDataStats, 100);
                }

                logUserAction('modal_opened', { modalId });
                updateActivity();
            } else {
                console.error('Modal not found:', modalId);
            }
        }

        function hideModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');

                // Reset forms
                const form = modal.querySelector('form');
                if (form) form.reset();

                logUserAction('modal_closed', { modalId });
                updateActivity();
            }
        }



        function processPayroll() {
            showAlert('Processing payroll for all employees...', 'info');
            setTimeout(() => {
                showAlert('Payroll processed successfully! Payslips are ready for distribution.', 'success');
            }, 2000);
            updateActivity();
        }

        function exportPayrollData() {
            showAlert('Exporting payroll data to Excel...', 'info');
            setTimeout(() => {
                showAlert('Payroll data exported successfully!', 'success');
            }, 1500);
            updateActivity();
        }

        function exportAllData() {
            showAlert('Exporting all financial data...', 'info');
            setTimeout(() => {
                showAlert('All data exported successfully!', 'success');
            }, 2000);
            updateActivity();
        }



        // Activity tracking for session management
        function setupActivityTracking() {
            const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
            events.forEach(event => {
                document.addEventListener(event, updateActivity, true);
            });
        }

        // Initialize Application
        document.addEventListener('DOMContentLoaded', function() {
            // Check for existing session first
            if (!checkAuthenticationOnLoad()) {
                // No valid session, show login page
                document.getElementById('loginPage').classList.remove('hidden');
                document.getElementById('dashboardPage').classList.add('hidden');
            }

            // Setup form handlers
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            document.getElementById('createUserForm').addEventListener('submit', handleCreateUser);
            // Invoice form handler is now managed by JS/Invoices.js
            document.getElementById('createExpenseForm').addEventListener('submit', handleCreateExpense);

            // Setup sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                document.getElementById('sidebar').classList.toggle('collapsed');
            });

            // Close modals when clicking outside
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal-overlay')) {
                    e.target.classList.add('hidden');
                }
            });

            // Setup activity tracking for session management
            setupActivityTracking();

            // Start dashboard auto-refresh
            startDashboardAutoRefresh();

            // Log application startup
            logUserAction('application_started');

            // Set current date for date inputs
            const today = new Date().toISOString().split('T')[0];
            const dateInputs = document.querySelectorAll('input[type="date"]');
            dateInputs.forEach(input => {
                if (!input.value) {
                    input.value = today;
                }
            });
        });

        // Global function for form submission (backup)
        window.handleCreateInvoice = function(event) {
            console.log('Global handleCreateInvoice called');
            if (window.invoicesManager) {
                window.invoicesManager.handleCreateInvoice(event);
            } else {
                console.error('InvoicesManager not available');
            }
        };

        // Additional global function for testing
        window.testInvoiceCreation = function() {
            console.log('=== TESTING INVOICE CREATION ===');

            if (!window.invoicesManager) {
                console.error('InvoicesManager not found');
                return;
            }

            console.log('InvoicesManager found');
            console.log('Customers available:', window.invoicesManager.customers.length);
            console.log('Invoices available:', window.invoicesManager.invoices.length);

            // Try to open the modal
            showModal('createInvoiceModal');
        };

        // Data Management Functions
        window.refreshDataStats = function() {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            const stats = window.invoicesManager.getDataStats();

            document.getElementById('statsInvoices').textContent = stats.totalInvoices;
            document.getElementById('statsCustomers').textContent = stats.totalCustomers;
            document.getElementById('statsPaid').textContent = stats.paidInvoices;
            document.getElementById('statsPending').textContent = stats.pendingInvoices;
            document.getElementById('statsRevenue').textContent = stats.totalRevenue.toLocaleString();
            document.getElementById('statsPendingAmount').textContent = stats.pendingAmount.toLocaleString();
        };

        window.exportAllData = function() {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            window.invoicesManager.exportData();
        };

        window.handleDataImport = function(input) {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            const file = input.files[0];
            if (file) {
                window.invoicesManager.importData(file);
                // Clear the input
                input.value = '';
                // Refresh stats after import
                setTimeout(refreshDataStats, 500);
            }
        };

        window.clearInvoicesOnly = function() {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            window.invoicesManager.clearAllInvoices();
            refreshDataStats();
        };

        window.clearCustomersOnly = function() {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            window.invoicesManager.clearAllCustomers();
            refreshDataStats();
        };

        window.resetEntireSystem = function() {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            window.invoicesManager.resetSystem();
            refreshDataStats();
        };

        // Real-time Dashboard Updates
        window.updateDashboardPendingRequests = function() {
            if (window.dashboardManager && currentPage === 'dashboard') {
                console.log('Updating dashboard pending requests...');
                window.dashboardManager.loadDashboardMetrics();
            }
        };

        // Listen for invoice data changes
        document.addEventListener('dataChanged', function(event) {
            if (event.detail.type === 'invoices') {
                console.log('Invoice data changed, updating dashboard...');
                updateDashboardPendingRequests();
            }
        });

        // Listen for invoice creation/updates from InvoicesManager
        document.addEventListener('invoiceCreated', function(event) {
            console.log('Invoice created, updating dashboard...');
            updateDashboardPendingRequests();
        });

        document.addEventListener('invoiceUpdated', function(event) {
            console.log('Invoice updated, updating dashboard...');
            updateDashboardPendingRequests();
        });

        document.addEventListener('invoiceDeleted', function(event) {
            console.log('Invoice deleted, updating dashboard...');
            updateDashboardPendingRequests();
        });
        // Customer Soft Delete Management Functions
        window.viewSoftDeletedCustomers = async function() {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            try {
                const softDeletedCustomers = await window.invoicesManager.getSoftDeletedCustomers();
                loadSoftDeletedCustomersTable(softDeletedCustomers);
                showModal('softDeletedCustomersModal');
            } catch (error) {
                console.error('Error loading soft deleted customers:', error);
                showAlert('Error loading deleted customers', 'error');
            }
        };

        window.loadSoftDeletedCustomersTable = function(customers) {
            const tbody = document.getElementById('softDeletedCustomersTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';

            if (customers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center" style="padding: 20px; color: #666;">
                            No deleted customers found.
                        </td>
                    </tr>
                `;
                return;
            }

            customers.forEach(customer => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${customer.name}</strong></td>
                    <td>${customer.email || 'N/A'}</td>
                    <td>${customer.phone || 'N/A'}</td>
                    <td>${customer.company || 'N/A'}</td>
                    <td>${customer.deletedAt ? new Date(customer.deletedAt).toLocaleDateString() : 'Unknown'}</td>
                    <td>
                        <div class="table-actions">
                            <button class="btn btn-sm btn-success" onclick="restoreCustomer(${customer.id})" title="Restore">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="permanentlyDeleteCustomer(${customer.id})" title="Permanently Delete">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        };

        window.restoreCustomer = async function(customerId) {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            try {
                await window.invoicesManager.restoreCustomer(customerId);
                refreshSoftDeletedCustomers();
                refreshDataStats();
            } catch (error) {
                console.error('Error restoring customer:', error);
                showAlert('Error restoring customer', 'error');
            }
        };

        window.permanentlyDeleteCustomer = async function(customerId) {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            try {
                await window.invoicesManager.permanentlyDeleteCustomer(customerId);
                refreshSoftDeletedCustomers();
                refreshDataStats();
            } catch (error) {
                console.error('Error permanently deleting customer:', error);
                showAlert('Error permanently deleting customer', 'error');
            }
        };

        window.refreshSoftDeletedCustomers = async function() {
            try {
                const softDeletedCustomers = await window.invoicesManager.getSoftDeletedCustomers();
                loadSoftDeletedCustomersTable(softDeletedCustomers);
            } catch (error) {
                console.error('Error refreshing soft deleted customers:', error);
                showAlert('Error refreshing deleted customers', 'error');
            }
        };

        window.showStorageStats = async function() {
            if (!window.invoicesManager) {
                showAlert('Invoice manager not available', 'error');
                return;
            }

            try {
                const stats = await window.invoicesManager.getStorageStats();
                if (stats) {
                    const message = `
📊 STORAGE STATISTICS

📄 INVOICES:
• Total Records: ${stats.invoices.totalRecords}
• Active Records: ${stats.invoices.activeRecords}
• Inactive Records: ${stats.invoices.inactiveRecords}
• File Size: ${(stats.invoices.fileSize / 1024).toFixed(2)} KB
• Last Modified: ${stats.invoices.lastModified ? new Date(stats.invoices.lastModified).toLocaleString() : 'Unknown'}

👥 CUSTOMERS:
• Total Records: ${stats.customers.totalRecords}
• Active Records: ${stats.customers.activeRecords}
• Inactive Records: ${stats.customers.inactiveRecords}
• File Size: ${(stats.customers.fileSize / 1024).toFixed(2)} KB
• Last Modified: ${stats.customers.lastModified ? new Date(stats.customers.lastModified).toLocaleString() : 'Unknown'}

🔧 SYSTEM:
• Storage Type: ${stats.isInitialized ? 'File Storage' : 'localStorage'}
• Total File Size: ${((stats.invoices.fileSize + stats.customers.fileSize) / 1024).toFixed(2)} KB
                    `;
                    alert(message);
                } else {
                    showAlert('Storage statistics not available', 'info');
                }
            } catch (error) {
                console.error('Error getting storage stats:', error);
                showAlert('Error getting storage statistics', 'error');
            }
        };

    </script>
</body>
</html>