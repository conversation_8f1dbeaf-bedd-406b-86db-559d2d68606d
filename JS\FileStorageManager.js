/**
 * FileStorageManager.js - JSON-based File Storage System
 * Handles persistent storage of invoice data with soft delete functionality
 */

class FileStorageManager {
    constructor() {
        this.invoicesPath = 'data/invoices.json';
        this.customersPath = 'data/customers.json';
        this.invoicesBackupPath = 'data/invoices_backup.json';
        this.customersBackupPath = 'data/customers_backup.json';
        this.isInitialized = false;
        this.invoicesCache = null;
        this.customersCache = null;
        this.invoicesLastModified = null;
        this.customersLastModified = null;
    }

    /**
     * Initialize the file storage system
     */
    async init() {
        try {
            console.log('Initializing FileStorageManager...');
            await this.ensureDataDirectory();
            await this.ensureInvoicesFile();
            await this.ensureCustomersFile();
            await this.loadInvoicesFromFile();
            await this.loadCustomersFromFile();
            this.isInitialized = true;
            console.log('FileStorageManager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize FileStorageManager:', error);
            // Fallback to localStorage if file system fails
            this.fallbackToLocalStorage();
        }
    }

    /**
     * Ensure data directory exists (browser simulation)
     */
    async ensureDataDirectory() {
        // In browser environment, we'll simulate directory structure
        // The actual file operations will be handled differently
        console.log('Data directory structure verified');
    }

    /**
     * Ensure invoices.json file exists
     */
    async ensureInvoicesFile() {
        try {
            // Try to load existing file
            await this.loadInvoicesFromFile();
        } catch (error) {
            // File doesn't exist, create it with empty array
            console.log('Creating new invoices.json file');
            await this.saveInvoicesToFile([]);
        }
    }

    /**
     * Ensure customers.json file exists
     */
    async ensureCustomersFile() {
        try {
            // Try to load existing file
            await this.loadCustomersFromFile();
        } catch (error) {
            // File doesn't exist, create it with empty array
            console.log('Creating new customers.json file');
            await this.saveCustomersToFile([]);
        }
    }

    /**
     * Load invoice data from JSON file
     */
    async loadInvoicesFromFile() {
        try {
            // In browser environment, we'll use a hybrid approach
            // Check if we have file data in localStorage first
            const fileData = localStorage.getItem('invoices_file_data');

            if (fileData) {
                const parsedData = JSON.parse(fileData);
                this.invoicesCache = parsedData;
                this.invoicesLastModified = localStorage.getItem('invoices_file_modified');
                console.log('Loaded invoice data from file storage:', this.invoicesCache.length, 'records');
                return this.invoicesCache;
            } else {
                // Initialize with empty array
                this.invoicesCache = [];
                await this.saveInvoicesToFile([]);
                return this.invoicesCache;
            }
        } catch (error) {
            console.error('Error loading invoices from file:', error);
            throw error;
        }
    }

    /**
     * Load customer data from JSON file
     */
    async loadCustomersFromFile() {
        try {
            // Check if we have customer file data in localStorage
            const fileData = localStorage.getItem('customers_file_data');

            if (fileData) {
                const parsedData = JSON.parse(fileData);
                this.customersCache = parsedData;
                this.customersLastModified = localStorage.getItem('customers_file_modified');
                console.log('Loaded customer data from file storage:', this.customersCache.length, 'records');
                return this.customersCache;
            } else {
                // Initialize with empty array
                this.customersCache = [];
                await this.saveCustomersToFile([]);
                return this.customersCache;
            }
        } catch (error) {
            console.error('Error loading customers from file:', error);
            throw error;
        }
    }

    /**
     * Save invoice data to JSON file
     */
    async saveInvoicesToFile(data) {
        try {
            // Create backup before saving
            if (this.invoicesCache && this.invoicesCache.length > 0) {
                await this.createInvoicesBackup();
            }

            // Validate data structure
            this.validateInvoiceData(data);

            // Save to localStorage as file simulation
            localStorage.setItem('invoices_file_data', JSON.stringify(data, null, 2));
            localStorage.setItem('invoices_file_modified', new Date().toISOString());

            // Update cache
            this.invoicesCache = data;
            this.invoicesLastModified = new Date().toISOString();

            console.log('Invoice data saved to file:', data.length, 'records');

            // Emit save event
            this.emitFileEvent('invoiceDataSaved', { count: data.length, timestamp: this.invoicesLastModified });

        } catch (error) {
            console.error('Error saving invoices to file:', error);
            throw error;
        }
    }

    /**
     * Save customer data to JSON file
     */
    async saveCustomersToFile(data) {
        try {
            // Create backup before saving
            if (this.customersCache && this.customersCache.length > 0) {
                await this.createCustomersBackup();
            }

            // Validate data structure
            this.validateCustomerData(data);

            // Save to localStorage as file simulation
            localStorage.setItem('customers_file_data', JSON.stringify(data, null, 2));
            localStorage.setItem('customers_file_modified', new Date().toISOString());

            // Update cache
            this.customersCache = data;
            this.customersLastModified = new Date().toISOString();

            console.log('Customer data saved to file:', data.length, 'records');

            // Emit save event
            this.emitFileEvent('customerDataSaved', { count: data.length, timestamp: this.customersLastModified });

        } catch (error) {
            console.error('Error saving customers to file:', error);
            throw error;
        }
    }

    /**
     * Get all invoices (including inactive ones)
     */
    async getAllInvoices() {
        if (!this.invoicesCache) {
            await this.loadInvoicesFromFile();
        }
        return this.invoicesCache || [];
    }

    /**
     * Get active invoices only (isActive: true)
     */
    async getActiveInvoices() {
        const allInvoices = await this.getAllInvoices();
        return allInvoices.filter(invoice => invoice.isActive !== false);
    }

    /**
     * Get inactive invoices (soft deleted)
     */
    async getInactiveInvoices() {
        const allInvoices = await this.getAllInvoices();
        return allInvoices.filter(invoice => invoice.isActive === false);
    }

    /**
     * Get all customers (including inactive ones)
     */
    async getAllCustomers() {
        if (!this.customersCache) {
            await this.loadCustomersFromFile();
        }
        return this.customersCache || [];
    }

    /**
     * Get active customers only (isActive: true)
     */
    async getActiveCustomers() {
        const allCustomers = await this.getAllCustomers();
        return allCustomers.filter(customer => customer.isActive !== false);
    }

    /**
     * Get inactive customers (soft deleted)
     */
    async getInactiveCustomers() {
        const allCustomers = await this.getAllCustomers();
        return allCustomers.filter(customer => customer.isActive === false);
    }

    /**
     * Add new invoice
     */
    async addInvoice(invoiceData) {
        try {
            const allInvoices = await this.getAllInvoices();
            
            // Ensure invoice has required fields
            const newInvoice = {
                ...invoiceData,
                id: invoiceData.id || Date.now(),
                isActive: true,
                createdAt: invoiceData.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Validate invoice structure
            this.validateInvoiceStructure(newInvoice);

            allInvoices.unshift(newInvoice);
            await this.saveInvoicesToFile(allInvoices);
            
            console.log('Invoice added to file storage:', newInvoice.number);
            return newInvoice;
        } catch (error) {
            console.error('Error adding invoice:', error);
            throw error;
        }
    }

    /**
     * Update existing invoice
     */
    async updateInvoice(invoiceId, updateData) {
        try {
            const allInvoices = await this.getAllInvoices();
            const index = allInvoices.findIndex(inv => inv.id === invoiceId);
            
            if (index === -1) {
                throw new Error(`Invoice with ID ${invoiceId} not found`);
            }

            // Update invoice data
            allInvoices[index] = {
                ...allInvoices[index],
                ...updateData,
                updatedAt: new Date().toISOString()
            };

            await this.saveInvoicesToFile(allInvoices);

            console.log('Invoice updated in file storage:', allInvoices[index].number);
            return allInvoices[index];
        } catch (error) {
            console.error('Error updating invoice:', error);
            throw error;
        }
    }

    /**
     * Soft delete invoice (set isActive: false)
     */
    async softDeleteInvoice(invoiceId) {
        try {
            const allInvoices = await this.getAllInvoices();
            const index = allInvoices.findIndex(inv => inv.id === invoiceId);

            if (index === -1) {
                throw new Error(`Invoice with ID ${invoiceId} not found`);
            }

            // Set isActive to false instead of removing
            allInvoices[index].isActive = false;
            allInvoices[index].deletedAt = new Date().toISOString();
            allInvoices[index].updatedAt = new Date().toISOString();

            await this.saveInvoicesToFile(allInvoices);

            console.log('Invoice soft deleted:', allInvoices[index].number);
            return allInvoices[index];
        } catch (error) {
            console.error('Error soft deleting invoice:', error);
            throw error;
        }
    }

    /**
     * Restore soft deleted invoice
     */
    async restoreInvoice(invoiceId) {
        try {
            const allInvoices = await this.getAllInvoices();
            const index = allInvoices.findIndex(inv => inv.id === invoiceId);

            if (index === -1) {
                throw new Error(`Invoice with ID ${invoiceId} not found`);
            }

            // Restore invoice by setting isActive to true
            allInvoices[index].isActive = true;
            delete allInvoices[index].deletedAt;
            allInvoices[index].updatedAt = new Date().toISOString();

            await this.saveInvoicesToFile(allInvoices);

            console.log('Invoice restored:', allInvoices[index].number);
            return allInvoices[index];
        } catch (error) {
            console.error('Error restoring invoice:', error);
            throw error;
        }
    }

    /**
     * Permanently delete invoice (hard delete)
     */
    async hardDeleteInvoice(invoiceId) {
        try {
            const allInvoices = await this.getAllInvoices();
            const index = allInvoices.findIndex(inv => inv.id === invoiceId);

            if (index === -1) {
                throw new Error(`Invoice with ID ${invoiceId} not found`);
            }

            const deletedInvoice = allInvoices.splice(index, 1)[0];
            await this.saveInvoicesToFile(allInvoices);
            
            console.log('Invoice permanently deleted:', deletedInvoice.number);
            return deletedInvoice;
        } catch (error) {
            console.error('Error permanently deleting invoice:', error);
            throw error;
        }
    }

    /**
     * Add new customer
     */
    async addCustomer(customerData) {
        try {
            const allCustomers = await this.getAllCustomers();

            // Ensure customer has required fields
            const newCustomer = {
                ...customerData,
                id: customerData.id || Date.now(),
                isActive: true,
                createdAt: customerData.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Validate customer structure
            this.validateCustomerStructure(newCustomer);

            allCustomers.unshift(newCustomer);
            await this.saveCustomersToFile(allCustomers);

            console.log('Customer added to file storage:', newCustomer.name);
            return newCustomer;
        } catch (error) {
            console.error('Error adding customer:', error);
            throw error;
        }
    }

    /**
     * Update existing customer
     */
    async updateCustomer(customerId, updateData) {
        try {
            const allCustomers = await this.getAllCustomers();
            const index = allCustomers.findIndex(cust => cust.id === customerId);

            if (index === -1) {
                throw new Error(`Customer with ID ${customerId} not found`);
            }

            // Update customer data
            allCustomers[index] = {
                ...allCustomers[index],
                ...updateData,
                updatedAt: new Date().toISOString()
            };

            await this.saveCustomersToFile(allCustomers);

            console.log('Customer updated in file storage:', allCustomers[index].name);
            return allCustomers[index];
        } catch (error) {
            console.error('Error updating customer:', error);
            throw error;
        }
    }

    /**
     * Soft delete customer (set isActive: false)
     */
    async softDeleteCustomer(customerId) {
        try {
            const allCustomers = await this.getAllCustomers();
            const index = allCustomers.findIndex(cust => cust.id === customerId);

            if (index === -1) {
                throw new Error(`Customer with ID ${customerId} not found`);
            }

            // Set isActive to false instead of removing
            allCustomers[index].isActive = false;
            allCustomers[index].deletedAt = new Date().toISOString();
            allCustomers[index].updatedAt = new Date().toISOString();

            await this.saveCustomersToFile(allCustomers);

            console.log('Customer soft deleted:', allCustomers[index].name);
            return allCustomers[index];
        } catch (error) {
            console.error('Error soft deleting customer:', error);
            throw error;
        }
    }

    /**
     * Restore soft deleted customer
     */
    async restoreCustomer(customerId) {
        try {
            const allCustomers = await this.getAllCustomers();
            const index = allCustomers.findIndex(cust => cust.id === customerId);

            if (index === -1) {
                throw new Error(`Customer with ID ${customerId} not found`);
            }

            // Restore customer by setting isActive to true
            allCustomers[index].isActive = true;
            delete allCustomers[index].deletedAt;
            allCustomers[index].updatedAt = new Date().toISOString();

            await this.saveCustomersToFile(allCustomers);

            console.log('Customer restored:', allCustomers[index].name);
            return allCustomers[index];
        } catch (error) {
            console.error('Error restoring customer:', error);
            throw error;
        }
    }

    /**
     * Permanently delete customer (hard delete)
     */
    async hardDeleteCustomer(customerId) {
        try {
            const allCustomers = await this.getAllCustomers();
            const index = allCustomers.findIndex(cust => cust.id === customerId);

            if (index === -1) {
                throw new Error(`Customer with ID ${customerId} not found`);
            }

            const deletedCustomer = allCustomers.splice(index, 1)[0];
            await this.saveCustomersToFile(allCustomers);

            console.log('Customer permanently deleted:', deletedCustomer.name);
            return deletedCustomer;
        } catch (error) {
            console.error('Error permanently deleting customer:', error);
            throw error;
        }
    }

    /**
     * Create backup of current invoice data
     */
    async createInvoicesBackup() {
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                data: this.invoicesCache,
                version: '1.0'
            };

            localStorage.setItem('invoices_backup_data', JSON.stringify(backupData, null, 2));
            console.log('Invoices backup created successfully');
        } catch (error) {
            console.error('Error creating invoices backup:', error);
        }
    }

    /**
     * Create backup of current customer data
     */
    async createCustomersBackup() {
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                data: this.customersCache,
                version: '1.0'
            };

            localStorage.setItem('customers_backup_data', JSON.stringify(backupData, null, 2));
            console.log('Customers backup created successfully');
        } catch (error) {
            console.error('Error creating customers backup:', error);
        }
    }

    /**
     * Restore from backup
     */
    async restoreFromBackup() {
        try {
            const backupData = localStorage.getItem('invoices_backup_data');
            if (!backupData) {
                throw new Error('No backup data found');
            }

            const backup = JSON.parse(backupData);
            await this.saveToFile(backup.data);
            
            console.log('Data restored from backup:', backup.timestamp);
            return backup.data;
        } catch (error) {
            console.error('Error restoring from backup:', error);
            throw error;
        }
    }

    /**
     * Validate invoice data array
     */
    validateInvoiceData(data) {
        if (!Array.isArray(data)) {
            throw new Error('Invoice data must be an array');
        }

        data.forEach((invoice, index) => {
            this.validateInvoiceStructure(invoice, index);
        });
    }

    /**
     * Validate individual invoice structure
     */
    validateInvoiceStructure(invoice, index = 0) {
        const requiredFields = ['id', 'number', 'amount', 'date', 'status'];

        for (const field of requiredFields) {
            if (invoice[field] === undefined || invoice[field] === null) {
                throw new Error(`Invoice at index ${index} missing required field: ${field}`);
            }
        }

        // Ensure isActive field exists
        if (invoice.isActive === undefined) {
            invoice.isActive = true;
        }
    }

    /**
     * Validate customer data array
     */
    validateCustomerData(data) {
        if (!Array.isArray(data)) {
            throw new Error('Customer data must be an array');
        }

        data.forEach((customer, index) => {
            this.validateCustomerStructure(customer, index);
        });
    }

    /**
     * Validate individual customer structure
     */
    validateCustomerStructure(customer, index = 0) {
        const requiredFields = ['id', 'name'];

        for (const field of requiredFields) {
            if (customer[field] === undefined || customer[field] === null) {
                throw new Error(`Customer at index ${index} missing required field: ${field}`);
            }
        }

        // Ensure isActive field exists
        if (customer.isActive === undefined) {
            customer.isActive = true;
        }
    }

    /**
     * Export data for external backup
     */
    async exportData() {
        try {
            const allInvoices = await this.getAllInvoices();
            const allCustomers = await this.getAllCustomers();

            const exportData = {
                timestamp: new Date().toISOString(),
                version: '1.0',
                invoices: {
                    totalRecords: allInvoices.length,
                    activeRecords: allInvoices.filter(inv => inv.isActive !== false).length,
                    inactiveRecords: allInvoices.filter(inv => inv.isActive === false).length,
                    data: allInvoices
                },
                customers: {
                    totalRecords: allCustomers.length,
                    activeRecords: allCustomers.filter(cust => cust.isActive !== false).length,
                    inactiveRecords: allCustomers.filter(cust => cust.isActive === false).length,
                    data: allCustomers
                }
            };

            return exportData;
        } catch (error) {
            console.error('Error exporting data:', error);
            throw error;
        }
    }

    /**
     * Import data from external source
     */
    async importData(importData) {
        try {
            let invoices;
            
            if (importData.data && Array.isArray(importData.data)) {
                invoices = importData.data;
            } else if (Array.isArray(importData)) {
                invoices = importData;
            } else {
                throw new Error('Invalid import data format');
            }

            // Validate imported data
            this.validateInvoiceData(invoices);

            // Create backup before import
            await this.createBackup();

            // Save imported data
            await this.saveToFile(invoices);
            
            console.log('Data imported successfully:', invoices.length, 'records');
            return invoices;
        } catch (error) {
            console.error('Error importing data:', error);
            throw error;
        }
    }

    /**
     * Get storage statistics
     */
    async getStorageStats() {
        try {
            const allInvoices = await this.getAllInvoices();
            const activeInvoices = allInvoices.filter(inv => inv.isActive !== false);
            const inactiveInvoices = allInvoices.filter(inv => inv.isActive === false);

            const allCustomers = await this.getAllCustomers();
            const activeCustomers = allCustomers.filter(cust => cust.isActive !== false);
            const inactiveCustomers = allCustomers.filter(cust => cust.isActive === false);

            return {
                invoices: {
                    totalRecords: allInvoices.length,
                    activeRecords: activeInvoices.length,
                    inactiveRecords: inactiveInvoices.length,
                    lastModified: this.invoicesLastModified,
                    fileSize: JSON.stringify(allInvoices).length
                },
                customers: {
                    totalRecords: allCustomers.length,
                    activeRecords: activeCustomers.length,
                    inactiveRecords: inactiveCustomers.length,
                    lastModified: this.customersLastModified,
                    fileSize: JSON.stringify(allCustomers).length
                },
                isInitialized: this.isInitialized
            };
        } catch (error) {
            console.error('Error getting storage stats:', error);
            return null;
        }
    }

    /**
     * Emit file storage events
     */
    emitFileEvent(eventType, data) {
        const event = new CustomEvent(`fileStorage${eventType}`, {
            detail: { ...data, timestamp: new Date().toISOString() }
        });
        document.dispatchEvent(event);
    }

    /**
     * Fallback to localStorage if file system fails
     */
    fallbackToLocalStorage() {
        console.warn('Falling back to localStorage due to file system error');
        this.isInitialized = true;
        this.cache = [];
    }

    /**
     * Clear all data (use with caution)
     */
    async clearAllData() {
        try {
            await this.createInvoicesBackup();
            await this.createCustomersBackup();
            await this.saveInvoicesToFile([]);
            await this.saveCustomersToFile([]);
            console.log('All invoice and customer data cleared');
        } catch (error) {
            console.error('Error clearing data:', error);
            throw error;
        }
    }

    /**
     * Clear invoice data only
     */
    async clearInvoiceData() {
        try {
            await this.createInvoicesBackup();
            await this.saveInvoicesToFile([]);
            console.log('All invoice data cleared');
        } catch (error) {
            console.error('Error clearing invoice data:', error);
            throw error;
        }
    }

    /**
     * Clear customer data only
     */
    async clearCustomerData() {
        try {
            await this.createCustomersBackup();
            await this.saveCustomersToFile([]);
            console.log('All customer data cleared');
        } catch (error) {
            console.error('Error clearing customer data:', error);
            throw error;
        }
    }
}

// Create global instance
window.fileStorageManager = new FileStorageManager();
