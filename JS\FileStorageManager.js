/**
 * FileStorageManager.js - JSON-based File Storage System
 * Handles persistent storage of invoice data with soft delete functionality
 */

class FileStorageManager {
    constructor() {
        this.invoicesPath = 'data/invoices.json';
        this.customersPath = 'data/customers.json';
        this.invoicesBackupPath = 'data/invoices_backup.json';
        this.customersBackupPath = 'data/customers_backup.json';
        this.isInitialized = false;
        this.invoicesCache = null;
        this.customersCache = null;
        this.invoicesLastModified = null;
        this.customersLastModified = null;
        this.apiBaseUrl = 'http://localhost:3000/api';
        this.useBackendAPI = true; // Try backend first, fallback to localStorage
    }

    /**
     * Initialize the file storage system
     */
    async init() {
        try {
            console.log('🔧 Initializing FileStorageManager...');

            // Check if backend API is available
            await this.checkBackendAPI();

            await this.ensureDataDirectory();
            await this.ensureInvoicesFile();
            await this.ensureCustomersFile();
            await this.loadInvoicesFromFile();
            await this.loadCustomersFromFile();
            this.isInitialized = true;
            console.log('✅ FileStorageManager initialized successfully');
            console.log('📊 Invoices in cache:', this.invoicesCache?.length || 0);
            console.log('📊 Customers in cache:', this.customersCache?.length || 0);
            console.log('🔗 Backend API available:', this.useBackendAPI);
        } catch (error) {
            console.error('❌ Failed to initialize FileStorageManager:', error);
            // Fallback to localStorage if file system fails
            this.fallbackToLocalStorage();
        }
    }

    /**
     * Check if backend API is available
     */
    async checkBackendAPI() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`, {
                method: 'GET',
                timeout: 5000
            });

            if (response.ok) {
                const data = await response.json();
                this.useBackendAPI = true;
                console.log('✅ Backend API is available:', data.message);
            } else {
                throw new Error('Backend API not responding');
            }
        } catch (error) {
            console.log('⚠️ Backend API not available, using localStorage fallback');
            this.useBackendAPI = false;
        }
    }

    /**
     * Ensure data directory exists (browser simulation)
     */
    async ensureDataDirectory() {
        // In browser environment, we'll simulate directory structure
        // The actual file operations will be handled differently
        console.log('Data directory structure verified');
    }

    /**
     * Ensure invoices.json file exists
     */
    async ensureInvoicesFile() {
        try {
            // Try to load existing file
            await this.loadInvoicesFromFile();
        } catch (error) {
            // File doesn't exist, create it with empty array
            console.log('Creating new invoices.json file');
            await this.saveInvoicesToFile([]);
        }
    }

    /**
     * Ensure customers.json file exists
     */
    async ensureCustomersFile() {
        try {
            // Try to load existing file
            await this.loadCustomersFromFile();
        } catch (error) {
            // File doesn't exist, create it with empty array
            console.log('Creating new customers.json file');
            await this.saveCustomersToFile([]);
        }
    }

    /**
     * Load invoice data from JSON file
     */
    async loadInvoicesFromFile() {
        try {
            // Try to load from backend API first
            if (this.useBackendAPI) {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/invoices`);
                    if (response.ok) {
                        const apiData = await response.json();
                        this.invoicesCache = Array.isArray(apiData) ? apiData : [];
                        this.invoicesLastModified = new Date().toISOString();

                        // Save to localStorage as backup
                        localStorage.setItem('invoices_file_data', JSON.stringify(this.invoicesCache, null, 2));
                        localStorage.setItem('invoices_file_backup', JSON.stringify(this.invoicesCache, null, 2));
                        localStorage.setItem('invoices_file_modified', this.invoicesLastModified);

                        console.log('✅ Loaded invoice data from backend API:', this.invoicesCache.length, 'records');
                        return this.invoicesCache;
                    }
                } catch (apiError) {
                    console.warn('⚠️ Backend API load failed, trying localStorage:', apiError.message);
                }
            }

            // Fallback to localStorage
            let fileData = localStorage.getItem('invoices_file_data');

            // If no main data, try backup
            if (!fileData) {
                fileData = localStorage.getItem('invoices_file_backup');
                if (fileData) {
                    console.log('🔄 Recovering invoices from backup...');
                    localStorage.setItem('invoices_file_data', fileData);
                }
            }

            if (fileData) {
                const parsedData = JSON.parse(fileData);
                this.invoicesCache = Array.isArray(parsedData) ? parsedData : [];
                this.invoicesLastModified = localStorage.getItem('invoices_file_modified') || new Date().toISOString();
                console.log('✅ Loaded invoice data from localStorage:', this.invoicesCache.length, 'records');

                // Ensure backup exists
                localStorage.setItem('invoices_file_backup', JSON.stringify(this.invoicesCache, null, 2));

                return this.invoicesCache;
            } else {
                // Try to load from the actual JSON file in data folder
                console.log('📁 No localStorage data found, trying to load from data/invoices.json...');
                try {
                    const response = await fetch('data/invoices.json');
                    if (response.ok) {
                        const jsonData = await response.json();
                        this.invoicesCache = Array.isArray(jsonData) ? jsonData : [];
                        await this.saveInvoicesToFile(this.invoicesCache);
                        console.log('✅ Loaded', this.invoicesCache.length, 'invoices from JSON file');
                        return this.invoicesCache;
                    }
                } catch (fetchError) {
                    console.log('📁 Could not load from JSON file, creating new empty file...');
                }

                // Initialize with empty array and save it immediately
                this.invoicesCache = [];
                await this.saveInvoicesToFile([]);
                console.log('✅ Created new empty invoice file');
                return this.invoicesCache;
            }
        } catch (error) {
            console.error('❌ Error loading invoices from file:', error);
            // Initialize with empty array as fallback
            this.invoicesCache = [];
            return this.invoicesCache;
        }
    }

    /**
     * Load customer data from JSON file
     */
    async loadCustomersFromFile() {
        try {
            // Try to load from backend API first
            if (this.useBackendAPI) {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/customers`);
                    if (response.ok) {
                        const apiData = await response.json();
                        this.customersCache = Array.isArray(apiData) ? apiData : [];
                        this.customersLastModified = new Date().toISOString();

                        // Save to localStorage as backup
                        localStorage.setItem('customers_file_data', JSON.stringify(this.customersCache, null, 2));
                        localStorage.setItem('customers_file_backup', JSON.stringify(this.customersCache, null, 2));
                        localStorage.setItem('customers_file_modified', this.customersLastModified);

                        console.log('✅ Loaded customer data from backend API:', this.customersCache.length, 'records');
                        return this.customersCache;
                    }
                } catch (apiError) {
                    console.warn('⚠️ Backend API load failed, trying localStorage:', apiError.message);
                }
            }

            // Fallback to localStorage
            let fileData = localStorage.getItem('customers_file_data');

            // If no main data, try backup
            if (!fileData) {
                fileData = localStorage.getItem('customers_file_backup');
                if (fileData) {
                    console.log('🔄 Recovering customers from backup...');
                    localStorage.setItem('customers_file_data', fileData);
                }
            }

            if (fileData) {
                const parsedData = JSON.parse(fileData);
                this.customersCache = Array.isArray(parsedData) ? parsedData : [];
                this.customersLastModified = localStorage.getItem('customers_file_modified') || new Date().toISOString();
                console.log('✅ Loaded customer data from localStorage:', this.customersCache.length, 'records');

                // Ensure backup exists
                localStorage.setItem('customers_file_backup', JSON.stringify(this.customersCache, null, 2));

                return this.customersCache;
            } else {
                // Try to load from the actual JSON file in data folder
                console.log('📁 No localStorage data found, trying to load from data/customers.json...');
                try {
                    const response = await fetch('data/customers.json');
                    if (response.ok) {
                        const jsonData = await response.json();
                        this.customersCache = Array.isArray(jsonData) ? jsonData : [];
                        await this.saveCustomersToFile(this.customersCache);
                        console.log('✅ Loaded', this.customersCache.length, 'customers from JSON file');
                        return this.customersCache;
                    }
                } catch (fetchError) {
                    console.log('📁 Could not load from JSON file, creating new empty file...');
                }

                // Initialize with empty array and save it immediately
                this.customersCache = [];
                await this.saveCustomersToFile([]);
                console.log('✅ Created new empty customer file');
                return this.customersCache;
            }
        } catch (error) {
            console.error('❌ Error loading customers from file:', error);
            // Initialize with empty array as fallback
            this.customersCache = [];
            return this.customersCache;
        }
    }

    /**
     * Save invoice data to JSON file
     */
    async saveInvoicesToFile(data) {
        try {
            // Ensure data is an array
            const dataToSave = Array.isArray(data) ? data : [];

            // Create backup before saving
            if (this.invoicesCache && this.invoicesCache.length > 0) {
                await this.createInvoicesBackup();
            }

            // Validate data structure
            this.validateInvoiceData(dataToSave);

            // Try to save to backend API first
            if (this.useBackendAPI) {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/invoices`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(dataToSave)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        console.log('✅ Invoice data saved to backend JSON file:', result.message);
                    } else {
                        throw new Error('Backend save failed');
                    }
                } catch (apiError) {
                    console.warn('⚠️ Backend API save failed, using localStorage fallback:', apiError.message);
                    this.useBackendAPI = false; // Disable for this session
                }
            }

            // Always save to localStorage as backup/fallback
            localStorage.setItem('invoices_file_data', JSON.stringify(dataToSave, null, 2));
            localStorage.setItem('invoices_file_modified', new Date().toISOString());
            localStorage.setItem('invoices_file_backup', JSON.stringify(dataToSave, null, 2));

            // Update cache
            this.invoicesCache = dataToSave;
            this.invoicesLastModified = new Date().toISOString();

            console.log('💾 Invoice data saved to storage:', dataToSave.length, 'records');

            // Emit save event
            this.emitFileEvent('invoiceDataSaved', { count: dataToSave.length, timestamp: this.invoicesLastModified });

        } catch (error) {
            console.error('❌ Error saving invoices to file:', error);
            throw error;
        }
    }

    /**
     * Save customer data to JSON file
     */
    async saveCustomersToFile(data) {
        try {
            // Ensure data is an array
            const dataToSave = Array.isArray(data) ? data : [];

            // Create backup before saving
            if (this.customersCache && this.customersCache.length > 0) {
                await this.createCustomersBackup();
            }

            // Validate data structure
            this.validateCustomerData(dataToSave);

            // Try to save to backend API first
            if (this.useBackendAPI) {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/customers`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(dataToSave)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        console.log('✅ Customer data saved to backend JSON file:', result.message);
                    } else {
                        throw new Error('Backend save failed');
                    }
                } catch (apiError) {
                    console.warn('⚠️ Backend API save failed, using localStorage fallback:', apiError.message);
                    this.useBackendAPI = false; // Disable for this session
                }
            }

            // Always save to localStorage as backup/fallback
            localStorage.setItem('customers_file_data', JSON.stringify(dataToSave, null, 2));
            localStorage.setItem('customers_file_modified', new Date().toISOString());
            localStorage.setItem('customers_file_backup', JSON.stringify(dataToSave, null, 2));

            // Update cache
            this.customersCache = dataToSave;
            this.customersLastModified = new Date().toISOString();

            console.log('💾 Customer data saved to storage:', dataToSave.length, 'records');

            // Emit save event
            this.emitFileEvent('customerDataSaved', { count: dataToSave.length, timestamp: this.customersLastModified });

        } catch (error) {
            console.error('❌ Error saving customers to file:', error);
            throw error;
        }
    }

    /**
     * Get all invoices (including inactive ones)
     */
    async getAllInvoices() {
        if (!this.invoicesCache) {
            await this.loadInvoicesFromFile();
        }
        return this.invoicesCache || [];
    }

    /**
     * Get active invoices only (isActive: true)
     */
    async getActiveInvoices() {
        const allInvoices = await this.getAllInvoices();
        return allInvoices.filter(invoice => invoice.isActive !== false);
    }

    /**
     * Get inactive invoices (soft deleted)
     */
    async getInactiveInvoices() {
        const allInvoices = await this.getAllInvoices();
        return allInvoices.filter(invoice => invoice.isActive === false);
    }

    /**
     * Get all customers (including inactive ones)
     */
    async getAllCustomers() {
        if (!this.customersCache) {
            await this.loadCustomersFromFile();
        }
        return this.customersCache || [];
    }

    /**
     * Get active customers only (isActive: true)
     */
    async getActiveCustomers() {
        const allCustomers = await this.getAllCustomers();
        return allCustomers.filter(customer => customer.isActive !== false);
    }

    /**
     * Get inactive customers (soft deleted)
     */
    async getInactiveCustomers() {
        const allCustomers = await this.getAllCustomers();
        return allCustomers.filter(customer => customer.isActive === false);
    }

    /**
     * Add new invoice
     */
    async addInvoice(invoiceData) {
        try {
            const allInvoices = await this.getAllInvoices();
            
            // Ensure invoice has required fields
            const newInvoice = {
                ...invoiceData,
                id: invoiceData.id || Date.now(),
                isActive: true,
                createdAt: invoiceData.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Validate invoice structure
            this.validateInvoiceStructure(newInvoice);

            allInvoices.unshift(newInvoice);
            await this.saveInvoicesToFile(allInvoices);
            
            console.log('Invoice added to file storage:', newInvoice.number);
            return newInvoice;
        } catch (error) {
            console.error('Error adding invoice:', error);
            throw error;
        }
    }

    /**
     * Update existing invoice
     */
    async updateInvoice(invoiceId, updateData) {
        try {
            const allInvoices = await this.getAllInvoices();
            const index = allInvoices.findIndex(inv => inv.id === invoiceId);
            
            if (index === -1) {
                throw new Error(`Invoice with ID ${invoiceId} not found`);
            }

            // Update invoice data
            allInvoices[index] = {
                ...allInvoices[index],
                ...updateData,
                updatedAt: new Date().toISOString()
            };

            await this.saveInvoicesToFile(allInvoices);

            console.log('Invoice updated in file storage:', allInvoices[index].number);
            return allInvoices[index];
        } catch (error) {
            console.error('Error updating invoice:', error);
            throw error;
        }
    }

    /**
     * Soft delete invoice (set isActive: false)
     */
    async softDeleteInvoice(invoiceId) {
        try {
            // Try backend API first
            if (this.useBackendAPI) {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/invoices/${invoiceId}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        const result = await response.json();
                        console.log('✅ Invoice soft deleted via backend API:', result.message);

                        // Update local cache
                        const allInvoices = await this.getAllInvoices();
                        const index = allInvoices.findIndex(inv => inv.id === invoiceId);
                        if (index !== -1) {
                            allInvoices[index].isActive = false;
                            allInvoices[index].deletedAt = new Date().toISOString();
                            allInvoices[index].updatedAt = new Date().toISOString();
                            this.invoicesCache = allInvoices;

                            // Update localStorage backup
                            localStorage.setItem('invoices_file_data', JSON.stringify(allInvoices, null, 2));
                            localStorage.setItem('invoices_file_backup', JSON.stringify(allInvoices, null, 2));
                        }

                        return allInvoices[index];
                    } else {
                        throw new Error('Backend delete failed');
                    }
                } catch (apiError) {
                    console.warn('⚠️ Backend API delete failed, using localStorage fallback:', apiError.message);
                    this.useBackendAPI = false;
                }
            }

            // Fallback to localStorage method
            const allInvoices = await this.getAllInvoices();
            const index = allInvoices.findIndex(inv => inv.id === invoiceId);

            if (index === -1) {
                throw new Error(`Invoice with ID ${invoiceId} not found`);
            }

            // Set isActive to false instead of removing
            allInvoices[index].isActive = false;
            allInvoices[index].deletedAt = new Date().toISOString();
            allInvoices[index].updatedAt = new Date().toISOString();

            await this.saveInvoicesToFile(allInvoices);

            console.log('✅ Invoice soft deleted:', allInvoices[index].number);
            return allInvoices[index];
        } catch (error) {
            console.error('❌ Error soft deleting invoice:', error);
            throw error;
        }
    }

    /**
     * Restore soft deleted invoice
     */
    async restoreInvoice(invoiceId) {
        try {
            const allInvoices = await this.getAllInvoices();
            const index = allInvoices.findIndex(inv => inv.id === invoiceId);

            if (index === -1) {
                throw new Error(`Invoice with ID ${invoiceId} not found`);
            }

            // Restore invoice by setting isActive to true
            allInvoices[index].isActive = true;
            delete allInvoices[index].deletedAt;
            allInvoices[index].updatedAt = new Date().toISOString();

            await this.saveInvoicesToFile(allInvoices);

            console.log('Invoice restored:', allInvoices[index].number);
            return allInvoices[index];
        } catch (error) {
            console.error('Error restoring invoice:', error);
            throw error;
        }
    }

    /**
     * Permanently delete invoice (hard delete)
     */
    async hardDeleteInvoice(invoiceId) {
        try {
            const allInvoices = await this.getAllInvoices();
            const index = allInvoices.findIndex(inv => inv.id === invoiceId);

            if (index === -1) {
                throw new Error(`Invoice with ID ${invoiceId} not found`);
            }

            const deletedInvoice = allInvoices.splice(index, 1)[0];
            await this.saveInvoicesToFile(allInvoices);
            
            console.log('Invoice permanently deleted:', deletedInvoice.number);
            return deletedInvoice;
        } catch (error) {
            console.error('Error permanently deleting invoice:', error);
            throw error;
        }
    }

    /**
     * Add new customer
     */
    async addCustomer(customerData) {
        try {
            const allCustomers = await this.getAllCustomers();

            // Ensure customer has required fields
            const newCustomer = {
                ...customerData,
                id: customerData.id || Date.now(),
                isActive: true,
                createdAt: customerData.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Validate customer structure
            this.validateCustomerStructure(newCustomer);

            allCustomers.unshift(newCustomer);
            await this.saveCustomersToFile(allCustomers);

            console.log('Customer added to file storage:', newCustomer.name);
            return newCustomer;
        } catch (error) {
            console.error('Error adding customer:', error);
            throw error;
        }
    }

    /**
     * Update existing customer
     */
    async updateCustomer(customerId, updateData) {
        try {
            const allCustomers = await this.getAllCustomers();
            const index = allCustomers.findIndex(cust => cust.id === customerId);

            if (index === -1) {
                throw new Error(`Customer with ID ${customerId} not found`);
            }

            // Update customer data
            allCustomers[index] = {
                ...allCustomers[index],
                ...updateData,
                updatedAt: new Date().toISOString()
            };

            await this.saveCustomersToFile(allCustomers);

            console.log('Customer updated in file storage:', allCustomers[index].name);
            return allCustomers[index];
        } catch (error) {
            console.error('Error updating customer:', error);
            throw error;
        }
    }

    /**
     * Soft delete customer (set isActive: false)
     */
    async softDeleteCustomer(customerId) {
        try {
            // Try backend API first
            if (this.useBackendAPI) {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/customers/${customerId}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        const result = await response.json();
                        console.log('✅ Customer soft deleted via backend API:', result.message);

                        // Update local cache
                        const allCustomers = await this.getAllCustomers();
                        const index = allCustomers.findIndex(cust => cust.id === customerId);
                        if (index !== -1) {
                            allCustomers[index].isActive = false;
                            allCustomers[index].deletedAt = new Date().toISOString();
                            allCustomers[index].updatedAt = new Date().toISOString();
                            this.customersCache = allCustomers;

                            // Update localStorage backup
                            localStorage.setItem('customers_file_data', JSON.stringify(allCustomers, null, 2));
                            localStorage.setItem('customers_file_backup', JSON.stringify(allCustomers, null, 2));
                        }

                        return allCustomers[index];
                    } else {
                        throw new Error('Backend delete failed');
                    }
                } catch (apiError) {
                    console.warn('⚠️ Backend API delete failed, using localStorage fallback:', apiError.message);
                    this.useBackendAPI = false;
                }
            }

            // Fallback to localStorage method
            const allCustomers = await this.getAllCustomers();
            const index = allCustomers.findIndex(cust => cust.id === customerId);

            if (index === -1) {
                throw new Error(`Customer with ID ${customerId} not found`);
            }

            // Set isActive to false instead of removing
            allCustomers[index].isActive = false;
            allCustomers[index].deletedAt = new Date().toISOString();
            allCustomers[index].updatedAt = new Date().toISOString();

            await this.saveCustomersToFile(allCustomers);

            console.log('✅ Customer soft deleted:', allCustomers[index].name);
            return allCustomers[index];
        } catch (error) {
            console.error('❌ Error soft deleting customer:', error);
            throw error;
        }
    }

    /**
     * Restore soft deleted customer
     */
    async restoreCustomer(customerId) {
        try {
            const allCustomers = await this.getAllCustomers();
            const index = allCustomers.findIndex(cust => cust.id === customerId);

            if (index === -1) {
                throw new Error(`Customer with ID ${customerId} not found`);
            }

            // Restore customer by setting isActive to true
            allCustomers[index].isActive = true;
            delete allCustomers[index].deletedAt;
            allCustomers[index].updatedAt = new Date().toISOString();

            await this.saveCustomersToFile(allCustomers);

            console.log('Customer restored:', allCustomers[index].name);
            return allCustomers[index];
        } catch (error) {
            console.error('Error restoring customer:', error);
            throw error;
        }
    }

    /**
     * Permanently delete customer (hard delete)
     */
    async hardDeleteCustomer(customerId) {
        try {
            const allCustomers = await this.getAllCustomers();
            const index = allCustomers.findIndex(cust => cust.id === customerId);

            if (index === -1) {
                throw new Error(`Customer with ID ${customerId} not found`);
            }

            const deletedCustomer = allCustomers.splice(index, 1)[0];
            await this.saveCustomersToFile(allCustomers);

            console.log('Customer permanently deleted:', deletedCustomer.name);
            return deletedCustomer;
        } catch (error) {
            console.error('Error permanently deleting customer:', error);
            throw error;
        }
    }

    /**
     * Create backup of current invoice data
     */
    async createInvoicesBackup() {
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                data: this.invoicesCache,
                version: '1.0'
            };

            localStorage.setItem('invoices_backup_data', JSON.stringify(backupData, null, 2));
            console.log('Invoices backup created successfully');
        } catch (error) {
            console.error('Error creating invoices backup:', error);
        }
    }

    /**
     * Create backup of current customer data
     */
    async createCustomersBackup() {
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                data: this.customersCache,
                version: '1.0'
            };

            localStorage.setItem('customers_backup_data', JSON.stringify(backupData, null, 2));
            console.log('Customers backup created successfully');
        } catch (error) {
            console.error('Error creating customers backup:', error);
        }
    }

    /**
     * Restore from backup
     */
    async restoreFromBackup() {
        try {
            const backupData = localStorage.getItem('invoices_backup_data');
            if (!backupData) {
                throw new Error('No backup data found');
            }

            const backup = JSON.parse(backupData);
            await this.saveToFile(backup.data);
            
            console.log('Data restored from backup:', backup.timestamp);
            return backup.data;
        } catch (error) {
            console.error('Error restoring from backup:', error);
            throw error;
        }
    }

    /**
     * Validate invoice data array
     */
    validateInvoiceData(data) {
        if (!Array.isArray(data)) {
            throw new Error('Invoice data must be an array');
        }

        data.forEach((invoice, index) => {
            this.validateInvoiceStructure(invoice, index);
        });
    }

    /**
     * Validate individual invoice structure
     */
    validateInvoiceStructure(invoice, index = 0) {
        const requiredFields = ['id', 'number', 'amount', 'date', 'status'];

        for (const field of requiredFields) {
            if (invoice[field] === undefined || invoice[field] === null) {
                throw new Error(`Invoice at index ${index} missing required field: ${field}`);
            }
        }

        // Ensure isActive field exists
        if (invoice.isActive === undefined) {
            invoice.isActive = true;
        }
    }

    /**
     * Validate customer data array
     */
    validateCustomerData(data) {
        if (!Array.isArray(data)) {
            throw new Error('Customer data must be an array');
        }

        data.forEach((customer, index) => {
            this.validateCustomerStructure(customer, index);
        });
    }

    /**
     * Validate individual customer structure
     */
    validateCustomerStructure(customer, index = 0) {
        const requiredFields = ['id', 'name'];

        for (const field of requiredFields) {
            if (customer[field] === undefined || customer[field] === null) {
                throw new Error(`Customer at index ${index} missing required field: ${field}`);
            }
        }

        // Ensure isActive field exists
        if (customer.isActive === undefined) {
            customer.isActive = true;
        }
    }

    /**
     * Export data for external backup
     */
    async exportData() {
        try {
            const allInvoices = await this.getAllInvoices();
            const allCustomers = await this.getAllCustomers();

            const exportData = {
                timestamp: new Date().toISOString(),
                version: '1.0',
                invoices: {
                    totalRecords: allInvoices.length,
                    activeRecords: allInvoices.filter(inv => inv.isActive !== false).length,
                    inactiveRecords: allInvoices.filter(inv => inv.isActive === false).length,
                    data: allInvoices
                },
                customers: {
                    totalRecords: allCustomers.length,
                    activeRecords: allCustomers.filter(cust => cust.isActive !== false).length,
                    inactiveRecords: allCustomers.filter(cust => cust.isActive === false).length,
                    data: allCustomers
                }
            };

            return exportData;
        } catch (error) {
            console.error('Error exporting data:', error);
            throw error;
        }
    }

    /**
     * Import data from external source
     */
    async importData(importData) {
        try {
            let invoices;
            
            if (importData.data && Array.isArray(importData.data)) {
                invoices = importData.data;
            } else if (Array.isArray(importData)) {
                invoices = importData;
            } else {
                throw new Error('Invalid import data format');
            }

            // Validate imported data
            this.validateInvoiceData(invoices);

            // Create backup before import
            await this.createBackup();

            // Save imported data
            await this.saveToFile(invoices);
            
            console.log('Data imported successfully:', invoices.length, 'records');
            return invoices;
        } catch (error) {
            console.error('Error importing data:', error);
            throw error;
        }
    }

    /**
     * Get storage statistics
     */
    async getStorageStats() {
        try {
            const allInvoices = await this.getAllInvoices();
            const activeInvoices = allInvoices.filter(inv => inv.isActive !== false);
            const inactiveInvoices = allInvoices.filter(inv => inv.isActive === false);

            const allCustomers = await this.getAllCustomers();
            const activeCustomers = allCustomers.filter(cust => cust.isActive !== false);
            const inactiveCustomers = allCustomers.filter(cust => cust.isActive === false);

            return {
                invoices: {
                    totalRecords: allInvoices.length,
                    activeRecords: activeInvoices.length,
                    inactiveRecords: inactiveInvoices.length,
                    lastModified: this.invoicesLastModified,
                    fileSize: JSON.stringify(allInvoices).length
                },
                customers: {
                    totalRecords: allCustomers.length,
                    activeRecords: activeCustomers.length,
                    inactiveRecords: inactiveCustomers.length,
                    lastModified: this.customersLastModified,
                    fileSize: JSON.stringify(allCustomers).length
                },
                isInitialized: this.isInitialized
            };
        } catch (error) {
            console.error('Error getting storage stats:', error);
            return null;
        }
    }

    /**
     * Emit file storage events
     */
    emitFileEvent(eventType, data) {
        const event = new CustomEvent(`fileStorage${eventType}`, {
            detail: { ...data, timestamp: new Date().toISOString() }
        });
        document.dispatchEvent(event);
    }

    /**
     * Fallback to localStorage if file system fails
     */
    fallbackToLocalStorage() {
        console.warn('Falling back to localStorage due to file system error');
        this.isInitialized = true;
        this.cache = [];
    }

    /**
     * Recover data from backup if main data is corrupted or missing
     */
    async recoverFromBackup() {
        try {
            console.log('🔄 Attempting data recovery from backup...');

            // Try to recover invoices
            const invoiceBackup = localStorage.getItem('invoices_file_backup');
            if (invoiceBackup) {
                const invoiceData = JSON.parse(invoiceBackup);
                if (Array.isArray(invoiceData)) {
                    localStorage.setItem('invoices_file_data', invoiceBackup);
                    this.invoicesCache = invoiceData;
                    console.log('✅ Recovered', invoiceData.length, 'invoices from backup');
                }
            }

            // Try to recover customers
            const customerBackup = localStorage.getItem('customers_file_backup');
            if (customerBackup) {
                const customerData = JSON.parse(customerBackup);
                if (Array.isArray(customerData)) {
                    localStorage.setItem('customers_file_data', customerBackup);
                    this.customersCache = customerData;
                    console.log('✅ Recovered', customerData.length, 'customers from backup');
                }
            }

            return true;
        } catch (error) {
            console.error('❌ Failed to recover from backup:', error);
            return false;
        }
    }

    /**
     * Force save data to ensure persistence
     */
    async forceSave() {
        try {
            if (this.invoicesCache) {
                await this.saveInvoicesToFile(this.invoicesCache);
            }
            if (this.customersCache) {
                await this.saveCustomersToFile(this.customersCache);
            }
            console.log('🔒 Force save completed');
        } catch (error) {
            console.error('❌ Force save failed:', error);
        }
    }

    /**
     * Clear all data (use with caution)
     */
    async clearAllData() {
        try {
            await this.createInvoicesBackup();
            await this.createCustomersBackup();
            await this.saveInvoicesToFile([]);
            await this.saveCustomersToFile([]);
            console.log('All invoice and customer data cleared');
        } catch (error) {
            console.error('Error clearing data:', error);
            throw error;
        }
    }

    /**
     * Clear invoice data only
     */
    async clearInvoiceData() {
        try {
            await this.createInvoicesBackup();
            await this.saveInvoicesToFile([]);
            console.log('All invoice data cleared');
        } catch (error) {
            console.error('Error clearing invoice data:', error);
            throw error;
        }
    }

    /**
     * Clear customer data only
     */
    async clearCustomerData() {
        try {
            await this.createCustomersBackup();
            await this.saveCustomersToFile([]);
            console.log('All customer data cleared');
        } catch (error) {
            console.error('Error clearing customer data:', error);
            throw error;
        }
    }
}

// Create global instance
window.fileStorageManager = new FileStorageManager();
