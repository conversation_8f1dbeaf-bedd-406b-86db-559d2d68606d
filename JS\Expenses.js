/**
 * Expenses.js - Comprehensive Expense Management System
 * Manages expenses, calculates revenue, total expenses, net profit, and Zakat (2.5% of net profit)
 * Integrates with employee salaries from User Management
 */

class ExpensesManager {
    constructor() {
        this.expenses = [];
        this.categories = [];
        this.isInitialized = false;
        this.currentFilter = 'all';
        this.currentSort = 'date';
        this.sortDirection = 'desc';
        this.zakatRate = 0.025; // 2.5% for Zakat calculation
    }

    /**
     * Initialize the expense management system
     */
    init() {
        if (this.isInitialized) return;
        
        this.loadData();
        this.setupEventListeners();
        this.loadExpensesTable();
        this.updateFinancialMetrics();
        this.setupRealTimeUpdates();
        
        this.isInitialized = true;
        console.log('Expenses Manager initialized successfully');
    }

    /**
     * Load data from localStorage or initialize with sample data
     */
    loadData() {
        // Load expenses
        const savedExpenses = localStorage.getItem('accounting_expenses');
        if (savedExpenses) {
            this.expenses = JSON.parse(savedExpenses);
        } else {
            this.initializeEmptyData();
        }

        // Load categories
        const savedCategories = localStorage.getItem('accounting_expense_categories');
        if (savedCategories) {
            this.categories = JSON.parse(savedCategories);
        } else {
            this.initializeCategories();
        }
    }

    /**
     * Initialize with empty expense data
     */
    initializeEmptyData() {
        console.log('Initializing empty expense data...');
        this.expenses = [];
        this.saveData();
    }

    /**
     * Initialize expense categories
     */
    initializeCategories() {
        this.categories = [
            { id: 'office-supplies', name: 'Office Supplies', color: '#FF6384' },
            { id: 'travel', name: 'Travel & Transportation', color: '#36A2EB' },
            { id: 'marketing', name: 'Marketing & Advertising', color: '#FFCE56' },
            { id: 'utilities', name: 'Utilities', color: '#4BC0C0' },
            { id: 'software', name: 'Software & Licenses', color: '#9966FF' },
            { id: 'meals', name: 'Meals & Entertainment', color: '#FF9F40' },
            { id: 'training', name: 'Training & Development', color: '#FF6384' },
            { id: 'equipment', name: 'Equipment & Hardware', color: '#36A2EB' },
            { id: 'legal', name: 'Legal & Professional', color: '#FFCE56' },
            { id: 'insurance', name: 'Insurance', color: '#4BC0C0' },
            { id: 'rent', name: 'Rent & Facilities', color: '#9966FF' },
            { id: 'other', name: 'Other Expenses', color: '#FF9F40' }
        ];
        this.saveCategories();
    }

    /**
     * Save data to localStorage
     */
    saveData() {
        localStorage.setItem('accounting_expenses', JSON.stringify(this.expenses));
        this.updateFinancialMetrics();
        this.notifyDashboard();
    }

    /**
     * Save categories to localStorage
     */
    saveCategories() {
        localStorage.setItem('accounting_expense_categories', JSON.stringify(this.categories));
    }

    /**
     * Create new expense
     */
    createExpense(expenseData) {
        const newExpense = {
            id: Date.now(),
            date: expenseData.date,
            amount: parseFloat(expenseData.amount),
            category: expenseData.category,
            description: expenseData.description,
            status: this.getCurrentUser().role === 'admin' ? 'approved' : 'pending',
            submittedBy: this.getCurrentUser().id,
            approvedBy: this.getCurrentUser().role === 'admin' ? this.getCurrentUser().id : null,
            receiptUrl: expenseData.receiptUrl || null,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        this.expenses.unshift(newExpense);
        this.saveData();
        this.loadExpensesTable();
        
        return newExpense;
    }

    /**
     * Update existing expense
     */
    updateExpense(expenseId, updateData) {
        const index = this.expenses.findIndex(exp => exp.id === expenseId);
        if (index === -1) return false;

        const expense = this.expenses[index];
        
        // Update fields
        Object.keys(updateData).forEach(key => {
            if (key !== 'id' && key !== 'createdAt') {
                expense[key] = updateData[key];
            }
        });

        expense.updatedAt = new Date();
        
        this.saveData();
        this.loadExpensesTable();
        
        return expense;
    }

    /**
     * Delete expense
     */
    deleteExpense(expenseId) {
        const index = this.expenses.findIndex(exp => exp.id === expenseId);
        if (index === -1) return false;

        this.expenses.splice(index, 1);
        this.saveData();
        this.loadExpensesTable();
        
        return true;
    }

    /**
     * Approve expense
     */
    approveExpense(expenseId) {
        const expense = this.expenses.find(exp => exp.id === expenseId);
        if (!expense) return false;

        expense.status = 'approved';
        expense.approvedBy = this.getCurrentUser().id;
        expense.approvedAt = new Date();
        expense.updatedAt = new Date();
        
        this.saveData();
        this.loadExpensesTable();
        
        return true;
    }

    /**
     * Reject expense
     */
    rejectExpense(expenseId, reason = '') {
        const expense = this.expenses.find(exp => exp.id === expenseId);
        if (!expense) return false;

        expense.status = 'rejected';
        expense.rejectedBy = this.getCurrentUser().id;
        expense.rejectedAt = new Date();
        expense.rejectionReason = reason;
        expense.updatedAt = new Date();
        
        this.saveData();
        this.loadExpensesTable();
        
        return true;
    }

    /**
     * Calculate comprehensive financial metrics
     */
    calculateFinancialMetrics() {
        // Get revenue from invoices
        const revenue = this.getTotalRevenue();
        
        // Calculate total expenses (approved only)
        const totalExpenses = this.getTotalExpenses();
        
        // Get employee salaries from payroll
        const totalSalaries = this.getTotalSalaries();
        
        // Calculate net profit
        const netProfit = revenue - totalExpenses - totalSalaries;
        
        // Calculate Zakat (2.5% of net profit, only if positive)
        const zakat = netProfit > 0 ? netProfit * this.zakatRate : 0;
        
        // Calculate expense breakdown by category
        const expensesByCategory = this.getExpensesByCategory();
        
        return {
            revenue,
            totalExpenses,
            totalSalaries,
            netProfit,
            zakat,
            expensesByCategory,
            totalCosts: totalExpenses + totalSalaries,
            profitMargin: revenue > 0 ? (netProfit / revenue) * 100 : 0
        };
    }

    /**
     * Get total revenue from invoices
     */
    getTotalRevenue() {
        if (window.invoicesManager) {
            const invoices = window.invoicesManager.getAllInvoices();
            return invoices
                .filter(inv => inv.status === 'paid')
                .reduce((sum, inv) => sum + inv.total, 0);
        }
        return 0;
    }

    /**
     * Get total approved expenses
     */
    getTotalExpenses() {
        return this.expenses
            .filter(exp => exp.status === 'approved')
            .reduce((sum, exp) => sum + exp.amount, 0);
    }

    /**
     * Get total employee salaries from payroll
     */
    getTotalSalaries() {
        if (window.payrollManager) {
            const employees = window.payrollManager.getAllEmployees();
            return employees
                .filter(emp => emp.isActive)
                .reduce((sum, emp) => sum + (emp.basicSalary || 0), 0);
        }
        return 0;
    }

    /**
     * Get expenses breakdown by category
     */
    getExpensesByCategory() {
        const breakdown = {};
        
        this.categories.forEach(category => {
            breakdown[category.id] = {
                name: category.name,
                amount: 0,
                count: 0,
                color: category.color
            };
        });

        this.expenses
            .filter(exp => exp.status === 'approved')
            .forEach(expense => {
                if (breakdown[expense.category]) {
                    breakdown[expense.category].amount += expense.amount;
                    breakdown[expense.category].count += 1;
                }
            });

        return breakdown;
    }

    /**
     * Update financial metrics display
     */
    updateFinancialMetrics() {
        const metrics = this.calculateFinancialMetrics();
        
        // Update metric cards
        this.updateMetricCard('totalRevenue', `$${metrics.revenue.toLocaleString()}`);
        this.updateMetricCard('totalExpenses', `$${metrics.totalExpenses.toLocaleString()}`);
        this.updateMetricCard('totalSalaries', `$${metrics.totalSalaries.toLocaleString()}`);
        this.updateMetricCard('totalCosts', `$${metrics.totalCosts.toLocaleString()}`);
        this.updateMetricCard('netProfit', `$${metrics.netProfit.toLocaleString()}`);
        this.updateMetricCard('zakat', `$${metrics.zakat.toLocaleString()}`);
        this.updateMetricCard('profitMargin', `${metrics.profitMargin.toFixed(1)}%`);

        // Update expense breakdown chart
        this.updateExpenseChart(metrics.expensesByCategory);
        
        // Update profit/loss indicator
        this.updateProfitLossIndicator(metrics.netProfit);
    }

    /**
     * Update metric card
     */
    updateMetricCard(metricId, value) {
        const element = document.getElementById(metricId);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * Update expense breakdown chart
     */
    updateExpenseChart(expensesByCategory) {
        const ctx = document.getElementById('expenseBreakdownChart');
        if (!ctx) return;

        const data = Object.values(expensesByCategory).filter(cat => cat.amount > 0);
        
        if (window.expenseChart) {
            window.expenseChart.destroy();
        }

        window.expenseChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(cat => cat.name),
                datasets: [{
                    data: data.map(cat => cat.amount),
                    backgroundColor: data.map(cat => cat.color)
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Expense Breakdown by Category'
                    },
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${context.label}: $${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Update profit/loss indicator
     */
    updateProfitLossIndicator(netProfit) {
        const indicator = document.getElementById('profitLossIndicator');
        if (!indicator) return;

        indicator.className = 'profit-loss-indicator';
        
        if (netProfit > 0) {
            indicator.classList.add('profit');
            indicator.innerHTML = `<i class="fas fa-arrow-up"></i> Profit: $${netProfit.toLocaleString()}`;
        } else if (netProfit < 0) {
            indicator.classList.add('loss');
            indicator.innerHTML = `<i class="fas fa-arrow-down"></i> Loss: $${Math.abs(netProfit).toLocaleString()}`;
        } else {
            indicator.classList.add('breakeven');
            indicator.innerHTML = `<i class="fas fa-minus"></i> Break Even`;
        }
    }

    /**
     * Load and display expenses table
     */
    loadExpensesTable() {
        const tbody = document.getElementById('expensesTableBody');
        if (!tbody) return;

        let filteredExpenses = this.getFilteredExpenses();
        filteredExpenses = this.getSortedExpenses(filteredExpenses);

        tbody.innerHTML = '';
        
        if (filteredExpenses.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">No expenses found</td>
                </tr>
            `;
            return;
        }

        filteredExpenses.forEach(expense => {
            const category = this.categories.find(cat => cat.id === expense.category);
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(expense.date).toLocaleDateString()}</td>
                <td>${expense.description}</td>
                <td>
                    <span class="category-badge" style="background-color: ${category?.color || '#ccc'}">
                        ${category?.name || expense.category}
                    </span>
                </td>
                <td>$${expense.amount.toLocaleString()}</td>
                <td><span class="status-badge ${expense.status}">${expense.status}</span></td>
                <td>${this.getUserName(expense.submittedBy)}</td>
                <td>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-secondary" onclick="expensesManager.viewExpense(${expense.id})" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="expensesManager.editExpense(${expense.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${expense.status === 'pending' ? `
                            <button class="btn btn-sm btn-success" onclick="expensesManager.approveExpenseConfirm(${expense.id})" title="Approve">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="expensesManager.rejectExpenseConfirm(${expense.id})" title="Reject">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-danger" onclick="expensesManager.deleteExpenseConfirm(${expense.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        this.updateExpenseCount(filteredExpenses.length);
    }

    /**
     * Get filtered expenses based on current filter
     */
    getFilteredExpenses() {
        if (this.currentFilter === 'all') {
            return this.expenses;
        }
        return this.expenses.filter(expense => expense.status === this.currentFilter);
    }

    /**
     * Get sorted expenses
     */
    getSortedExpenses(expenses) {
        return expenses.sort((a, b) => {
            let aValue = a[this.currentSort];
            let bValue = b[this.currentSort];

            // Handle date sorting
            if (this.currentSort === 'date') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            }

            // Handle numeric sorting
            if (this.currentSort === 'amount') {
                aValue = parseFloat(aValue);
                bValue = parseFloat(bValue);
            }

            if (this.sortDirection === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }

    /**
     * Update expense count display
     */
    updateExpenseCount(count) {
        const countElement = document.getElementById('expenseCount');
        if (countElement) {
            countElement.textContent = `${count} expense${count !== 1 ? 's' : ''}`;
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Filter buttons
        document.querySelectorAll('.expense-filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // Sort headers
        document.querySelectorAll('.expense-sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                this.setSorting(e.target.dataset.sort);
            });
        });

        // Search input
        const searchInput = document.getElementById('expenseSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchExpenses(e.target.value);
            });
        }

        // Create expense form
        const createForm = document.getElementById('createExpenseForm');
        if (createForm) {
            createForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCreateExpense(e);
            });
        }
    }

    /**
     * Set current filter
     */
    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update active filter button
        document.querySelectorAll('.expense-filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`)?.classList.add('active');
        
        this.loadExpensesTable();
    }

    /**
     * Set sorting
     */
    setSorting(sortField) {
        if (this.currentSort === sortField) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort = sortField;
            this.sortDirection = 'desc';
        }
        
        this.loadExpensesTable();
    }

    /**
     * Search expenses
     */
    searchExpenses(query) {
        const filteredExpenses = this.expenses.filter(expense => 
            expense.description.toLowerCase().includes(query.toLowerCase()) ||
            expense.category.toLowerCase().includes(query.toLowerCase()) ||
            expense.status.toLowerCase().includes(query.toLowerCase())
        );

        this.displaySearchResults(filteredExpenses);
    }

    /**
     * Display search results
     */
    displaySearchResults(expenses) {
        // Similar to loadExpensesTable but with filtered results
        this.loadExpensesTable();
    }

    /**
     * Handle create expense form submission
     */
    handleCreateExpense(event) {
        const formData = new FormData(event.target);
        const expenseData = Object.fromEntries(formData);

        // Validation
        if (!expenseData.date || !expenseData.amount || !expenseData.category || !expenseData.description) {
            this.showAlert('Please fill in all required fields.', 'error');
            return;
        }

        if (parseFloat(expenseData.amount) <= 0) {
            this.showAlert('Expense amount must be greater than zero.', 'error');
            return;
        }

        const newExpense = this.createExpense(expenseData);
        const statusMsg = newExpense.status === 'approved' ? 'approved automatically' : 'submitted for approval';
        this.showAlert(`Expense of $${newExpense.amount.toLocaleString()} ${statusMsg}!`, 'success');
        
        // Reset form and close modal
        event.target.reset();
        this.hideModal('createExpenseModal');
    }

    /**
     * Setup real-time updates
     */
    setupRealTimeUpdates() {
        // Auto-save every 30 seconds
        setInterval(() => {
            this.saveData();
        }, 30000);

        // Update metrics every minute
        setInterval(() => {
            this.updateFinancialMetrics();
        }, 60000);
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return window.currentUser || { id: 1, role: 'admin' };
    }

    /**
     * Get user name by ID
     */
    getUserName(userId) {
        if (window.users) {
            const user = window.users.find(u => u.id === userId);
            return user ? `${user.firstName} ${user.lastName}` : 'Unknown User';
        }
        return 'Unknown User';
    }

    /**
     * View expense details
     */
    viewExpense(expenseId) {
        const expense = this.expenses.find(exp => exp.id === expenseId);
        if (!expense) return;

        console.log('Viewing expense:', expense);
        this.showAlert('Expense details view would open here.', 'info');
    }

    /**
     * Edit expense
     */
    editExpense(expenseId) {
        const expense = this.expenses.find(exp => exp.id === expenseId);
        if (!expense) return;

        console.log('Editing expense:', expense);
        this.showAlert('Expense edit form would open here.', 'info');
    }

    /**
     * Approve expense with confirmation
     */
    approveExpenseConfirm(expenseId) {
        const expense = this.expenses.find(exp => exp.id === expenseId);
        if (!expense) return;

        if (confirm(`Are you sure you want to approve this expense of $${expense.amount.toLocaleString()}?`)) {
            if (this.approveExpense(expenseId)) {
                this.showAlert('Expense approved successfully!', 'success');
            }
        }
    }

    /**
     * Reject expense with confirmation
     */
    rejectExpenseConfirm(expenseId) {
        const expense = this.expenses.find(exp => exp.id === expenseId);
        if (!expense) return;

        const reason = prompt('Please provide a reason for rejection (optional):');
        if (reason !== null) { // User didn't cancel
            if (this.rejectExpense(expenseId, reason)) {
                this.showAlert('Expense rejected successfully!', 'success');
            }
        }
    }

    /**
     * Delete expense with confirmation
     */
    deleteExpenseConfirm(expenseId) {
        const expense = this.expenses.find(exp => exp.id === expenseId);
        if (!expense) return;

        if (confirm(`Are you sure you want to delete this expense? This action cannot be undone.`)) {
            if (this.deleteExpense(expenseId)) {
                this.showAlert('Expense deleted successfully!', 'success');
            }
        }
    }

    /**
     * Get all expenses (for dashboard integration)
     */
    getAllExpenses() {
        return this.expenses;
    }

    /**
     * Show alert message
     */
    showAlert(message, type) {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        if (window.showAlert) {
            window.showAlert(message, type);
        }
    }

    /**
     * Hide modal
     */
    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    /**
     * Notify dashboard of changes
     */
    notifyDashboard() {
        if (window.dashboardManager) {
            window.dashboardManager.loadDashboardMetrics();
        }
    }
}

// Create global instance
window.expensesManager = new ExpensesManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.expensesManager.init();
    });
} else {
    window.expensesManager.init();
}
