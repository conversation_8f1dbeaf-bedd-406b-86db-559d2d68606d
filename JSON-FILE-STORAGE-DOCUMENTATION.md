# JSON-Based File Storage System Documentation

## 🎯 Overview

The accounting software now implements a comprehensive JSON-based file storage system with soft delete functionality, providing persistent data storage, audit trails, and data recovery capabilities.

## 📁 File Structure

```
project-root/
├── data/
│   ├── invoices.json          # Main invoice data storage
│   └── invoices_backup.json   # Automatic backup storage
├── JS/
│   ├── FileStorageManager.js  # File storage management class
│   └── Invoices.js           # Updated with file storage integration
└── index.html                # Updated with file storage UI
```

## 📋 JSON Schema

### Invoice Object Structure
```json
{
  "id": *************,
  "number": "INV-001",
  "customerId": 1,
  "customerName": "ABC Corporation",
  "amount": 1000.00,
  "date": "2024-01-15",
  "dueDate": "2024-02-15",
  "status": "pending",
  "subtotal": 1000.00,
  "tax": 150.00,
  "total": 1150.00,
  "notes": "Monthly consulting services",
  "items": [],
  "isActive": true,
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z",
  "deletedAt": null
}
```

### Required Fields
- `id`: Unique identifier (timestamp-based)
- `number`: Invoice number (e.g., "INV-001")
- `amount`: Invoice amount (numeric)
- `date`: Invoice creation date
- `status`: Payment status ("pending", "paid", "overdue")
- `isActive`: Boolean controlling visibility (true/false)

### Optional Fields
- `customer`: Customer name or object
- `dueDate`: Payment due date
- `subtotal`, `tax`, `total`: Financial calculations
- `notes`: Additional information
- `items`: Array of invoice line items
- `createdAt`, `updatedAt`: Timestamps
- `deletedAt`: Soft delete timestamp

## 🔧 Implementation Details

### FileStorageManager Class

#### Core Methods:
```javascript
// Initialize storage system
await fileStorageManager.init()

// Data operations
await fileStorageManager.addInvoice(invoiceData)
await fileStorageManager.updateInvoice(id, updateData)
await fileStorageManager.softDeleteInvoice(id)

// Retrieval methods
await fileStorageManager.getActiveInvoices()
await fileStorageManager.getInactiveInvoices()
await fileStorageManager.getAllInvoices()

// Recovery operations
await fileStorageManager.restoreInvoice(id)
await fileStorageManager.hardDeleteInvoice(id)
```

### InvoicesManager Integration

#### Updated Methods:
- `createInvoice()`: Now saves to JSON file
- `updateInvoice()`: Updates file storage
- `deleteInvoice()`: Implements soft delete
- `loadData()`: Loads from file storage
- `saveData()`: Saves to file storage

## 🗑️ Soft Delete System

### How It Works:
1. **User Deletes Invoice**: UI removes invoice from display
2. **Soft Delete**: Sets `isActive: false` in JSON file
3. **Data Preservation**: Invoice remains in file for recovery
4. **UI Filtering**: Only `isActive: true` invoices are shown

### Benefits:
- **Data Recovery**: Restore accidentally deleted invoices
- **Audit Trail**: Maintain complete transaction history
- **Compliance**: Meet data retention requirements
- **Safety**: Prevent permanent data loss

### Operations:
```javascript
// Soft delete (hides from UI, preserves in file)
await invoicesManager.deleteInvoice(invoiceId)

// View soft deleted invoices
const deletedInvoices = await invoicesManager.getSoftDeletedInvoices()

// Restore soft deleted invoice
await invoicesManager.restoreInvoice(invoiceId)

// Permanently delete (removes from file)
await invoicesManager.permanentlyDeleteInvoice(invoiceId)
```

## 🎛️ User Interface Features

### Data Management Modal
**Location**: Settings → System Management → Data Management

#### Features:
1. **📊 Data Statistics**: Real-time storage metrics
2. **💾 Export/Import**: Backup and restore functionality
3. **🗑️ Data Clearing**: Selective data management
4. **🗂️ Soft Delete Management**: View and manage deleted invoices
5. **📈 Storage Statistics**: File size and record counts

### Soft Delete Management
**Access**: Data Management → Soft Delete Management

#### Capabilities:
- **View Deleted Invoices**: See all soft deleted records
- **Restore Invoices**: Bring back deleted invoices
- **Permanent Delete**: Remove invoices completely
- **Storage Statistics**: Monitor file storage usage

## 🔄 Data Flow

### Create Invoice:
```
User Input → InvoicesManager.createInvoice() → FileStorageManager.addInvoice() → JSON File
```

### Update Invoice:
```
User Input → InvoicesManager.updateInvoice() → FileStorageManager.updateInvoice() → JSON File
```

### Delete Invoice (Soft):
```
User Action → InvoicesManager.deleteInvoice() → FileStorageManager.softDeleteInvoice() → Set isActive: false
```

### Load Invoices:
```
Page Load → InvoicesManager.loadDataFromFile() → FileStorageManager.getActiveInvoices() → UI Display
```

## 🛡️ Error Handling

### Fallback System:
1. **Primary**: JSON file storage
2. **Fallback**: localStorage storage
3. **Error Recovery**: Automatic backup restoration

### Error Scenarios:
- **File Access Failure**: Falls back to localStorage
- **JSON Parse Error**: Attempts backup restoration
- **Storage Full**: Provides user notification
- **Corruption Detection**: Validates data integrity

## 📊 Storage Statistics

### Metrics Tracked:
- **Total Records**: All invoices (active + inactive)
- **Active Records**: Visible invoices (`isActive: true`)
- **Inactive Records**: Soft deleted invoices (`isActive: false`)
- **File Size**: JSON file size in bytes
- **Last Modified**: Timestamp of last update

### Access Methods:
```javascript
// Get comprehensive storage statistics
const stats = await invoicesManager.getStorageStats()

// Display in UI
showStorageStats() // Shows popup with statistics
```

## 🔧 Configuration Options

### FileStorageManager Settings:
```javascript
// Enable/disable file storage
this.useFileStorage = true

// File paths
this.dataPath = 'data/invoices.json'
this.backupPath = 'data/invoices_backup.json'

// Caching
this.cache = null
this.lastModified = null
```

## 🧪 Testing the System

### Basic Operations:
1. **Create Invoice**: Verify saved to JSON file
2. **Update Invoice**: Check file reflects changes
3. **Delete Invoice**: Confirm soft delete (isActive: false)
4. **Restore Invoice**: Verify restoration works
5. **View Statistics**: Check accurate metrics

### Advanced Testing:
1. **File Corruption**: Test backup restoration
2. **Storage Failure**: Verify localStorage fallback
3. **Large Datasets**: Test performance with many records
4. **Concurrent Operations**: Test multiple simultaneous changes

## 🚀 Benefits

### For Users:
- **Data Safety**: Accidental deletions are recoverable
- **Audit Trail**: Complete transaction history
- **Performance**: Fast file-based storage
- **Reliability**: Automatic backup system

### For Business:
- **Compliance**: Meet data retention requirements
- **Recovery**: Restore lost data easily
- **Analytics**: Access to complete historical data
- **Scalability**: JSON format supports growth

## 🔮 Future Enhancements

### Potential Additions:
- **Compression**: Reduce file size for large datasets
- **Encryption**: Secure sensitive financial data
- **Versioning**: Track changes over time
- **Cloud Sync**: Synchronize across devices
- **Real-time Collaboration**: Multi-user support

## 📝 Migration Guide

### From localStorage to File Storage:
1. **Automatic Migration**: System detects existing localStorage data
2. **Data Preservation**: Existing invoices are migrated to JSON format
3. **Backward Compatibility**: Falls back to localStorage if needed
4. **No Data Loss**: Migration preserves all existing information

The JSON-based file storage system provides a robust, scalable, and user-friendly solution for persistent invoice data management with comprehensive soft delete capabilities and data recovery features.
