/* CSS Variables for theming */
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --info-color: #06b6d4;
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --spacing-1: 0.25rem;
            --spacing-2: 0.5rem;
            --spacing-3: 0.75rem;
            --spacing-4: 1rem;
            --spacing-5: 1.25rem;
            --spacing-6: 1.5rem;
            --spacing-8: 2rem;
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --transition-fast: 150ms ease-in-out;
            --transition-normal: 300ms ease-in-out;
        }



        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-family);
            font-size: 1rem;
            line-height: 1.6;
            color: var(--gray-900);
            background-color: var(--gray-50);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.25;
            margin-bottom: var(--spacing-4);
        }

        h1 { font-size: 1.875rem; }
        h2 { font-size: 1.5rem; }
        h3 { font-size: 1.25rem; }
        h4 { font-size: 1.125rem; }

        p {
            margin-bottom: var(--spacing-4);
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color var(--transition-fast);
        }

        a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-2);
            padding: var(--spacing-3) var(--spacing-6);
            border: none;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all var(--transition-fast);
            min-height: 44px;
            width: 100%;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background-color: var(--gray-100);
            color: var(--gray-700);
            border: 1px solid var(--gray-200);
        }

        .btn-secondary:hover {
            background-color: var(--gray-200);
            color: var(--gray-800);
        }

        .btn-success {
            background-color: var(--success-color);
            color: var(--white);
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: var(--white);
        }

        .btn-danger {
            background-color: var(--error-color);
            color: var(--white);
        }

        .btn-sm {
            padding: var(--spacing-1) var(--spacing-3);
            font-size: 0.875rem;
            min-height: auto;
            width: auto;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: var(--spacing-5);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-4);
        }

        label {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: var(--spacing-2);
            font-size: 0.875rem;
        }

        input, select, textarea {
            width: 100%;
            padding: var(--spacing-3) var(--spacing-4);
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
            background-color: var(--white);
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        input::placeholder {
            color: var(--gray-400);
        }

        /* Login Page Styles */
        .login-page {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-4);
        }

        .login-container {
            width: 100%;
            max-width: 450px;
        }

        .login-card {
            background: var(--white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            padding: var(--spacing-8);
            margin-bottom: var(--spacing-6);
        }

        .login-header {
            text-align: center;
            margin-bottom: var(--spacing-8);
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-3);
            margin-bottom: var(--spacing-4);
        }

        .logo i {
            font-size: 1.875rem;
            color: var(--primary-color);
        }

        .logo h1 {
            color: var(--gray-900);
            margin: 0;
        }

        .subtitle {
            color: var(--gray-600);
            font-size: 0.875rem;
            margin: 0;
        }

        .password-input {
            position: relative;
        }

        .toggle-password {
            position: absolute;
            right: var(--spacing-3);
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--gray-400);
            cursor: pointer;
            padding: var(--spacing-2);
            transition: color var(--transition-fast);
        }

        .toggle-password:hover {
            color: var(--gray-600);
        }

        /* Dashboard Layout */
        .dashboard-page {
            display: flex;
            min-height: 100vh;
            background-color: var(--gray-50);
        }

        .sidebar {
            width: 280px;
            background-color: var(--white);
            border-right: 1px solid var(--gray-200);
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            transition: transform var(--transition-normal);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .main-content {
            flex: 1;
            margin-left: 280px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            transition: margin-left var(--transition-normal);
        }

        .sidebar.collapsed + .main-content {
            margin-left: 70px;
        }

        /* Sidebar Styles */
        .sidebar-header {
            padding: var(--spacing-6) var(--spacing-4);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .sidebar-header .logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            font-weight: 600;
            color: var(--gray-900);
        }

        .sidebar-header .logo i {
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            padding: var(--spacing-2);
            cursor: pointer;
            color: var(--gray-600);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .sidebar-toggle:hover {
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        .sidebar-menu {
            flex: 1;
            padding: var(--spacing-4) 0;
            overflow-y: auto;
        }

        .menu-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .menu-item {
            margin-bottom: var(--spacing-1);
        }

        .menu-link {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            padding: var(--spacing-3) var(--spacing-4);
            color: var(--gray-700);
            text-decoration: none;
            transition: all var(--transition-fast);
            position: relative;
        }

        .menu-link:hover {
            background-color: var(--gray-50);
            color: var(--primary-color);
            text-decoration: none;
        }

        .menu-item.active .menu-link {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .menu-link i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        .sidebar-footer {
            padding: var(--spacing-4);
            border-top: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            flex: 1;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            color: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .user-name {
            font-weight: 500;
            color: var(--gray-900);
            font-size: 0.875rem;
        }

        .user-role {
            color: var(--gray-500);
            font-size: 0.75rem;
            text-transform: capitalize;
        }

        .logout-btn {
            background: none;
            border: none;
            padding: var(--spacing-2);
            cursor: pointer;
            color: var(--gray-600);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .logout-btn:hover {
            background-color: var(--error-color);
            color: var(--white);
        }

        /* Top Header */
        .top-header {
            background-color: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: var(--spacing-4) var(--spacing-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: var(--spacing-4);
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
        }

        .header-btn {
            background: none;
            border: none;
            padding: var(--spacing-2);
            cursor: pointer;
            color: var(--gray-600);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .header-btn:hover {
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        /* Dashboard Content */
        .dashboard-content {
            padding: var(--spacing-6);
            flex: 1;
        }

        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-6);
            margin-bottom: var(--spacing-8);
        }

        .metric-card {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-6);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: var(--spacing-4);
            transition: all var(--transition-fast);
        }

        .metric-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: var(--white);
        }

        .metric-icon.success {
            background-color: var(--success-color);
        }

        .metric-icon.warning {
            background-color: var(--warning-color);
        }

        .metric-icon.info {
            background-color: var(--info-color);
        }

        .metric-icon.primary {
            background-color: var(--primary-color);
        }

        .metric-info {
            flex: 1;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0 0 var(--spacing-1) 0;
        }

        .metric-label {
            color: var(--gray-600);
            font-size: 0.875rem;
            margin: 0 0 var(--spacing-2) 0;
        }

        .metric-change {
            font-size: 0.75rem;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }

        .metric-change.positive {
            background-color: #d1fae5;
            color: #065f46;
        }

        .metric-change.negative {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .metric-change.neutral {
            background-color: var(--gray-100);
            color: var(--gray-600);
        }

        .metric-change.warning {
            background-color: #fef3c7;
            color: #92400e;
            font-weight: 600;
        }

        /* Pending Requests Counter Specific Styling */
        [data-metric="pendingRequests"] {
            border-left: 4px solid var(--warning-color);
            position: relative;
        }

        [data-metric="pendingRequests"]:hover {
            border-left-color: #d97706;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
        }

        [data-metric="pendingRequests"] .metric-value {
            font-size: 1.75rem;
            font-weight: 800;
        }

        [data-metric="pendingRequests"] .metric-change {
            font-size: 0.875rem;
            font-weight: 600;
            padding: 4px 8px;
        }

        /* Pulse animation for pending requests when count > 0 */
        [data-metric="pendingRequests"].has-pending {
            animation: pendingPulse 2s infinite;
        }

        @keyframes pendingPulse {
            0% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(245, 158, 11, 0); }
            100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0); }
        }

        /* Quick Actions */
        .quick-actions {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-6);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--spacing-8);
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-4);
            margin-top: var(--spacing-4);
        }

        /* Table Action Buttons - Smaller and Inline */
        .table-actions {
            display: flex;
            gap: 4px;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: nowrap;
        }

        .table-actions .btn {
            padding: 4px 8px;
            min-height: 28px;
            width: auto;
            font-size: 12px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .table-actions .btn i {
            font-size: 12px;
            margin: 0;
        }

        .table-actions .btn-sm {
            padding: 3px 6px;
            min-height: 24px;
            font-size: 11px;
        }

        /* Ensure table cells don't expand */
        .data-table td:last-child {
            width: 140px;
            white-space: nowrap;
            padding: 4px 8px;
        }

        /* Hover effects for table action buttons */
        .table-actions .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* Button color variations for better UX */
        .table-actions .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        .table-actions .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }

        .table-actions .btn-primary:hover {
            background-color: #0056b3;
        }

        .table-actions .btn-success:hover {
            background-color: #218838;
        }

        .table-actions .btn-warning:hover {
            background-color: #e0a800;
        }

        .table-actions .btn-danger:hover {
            background-color: #c82333;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-2);
            padding: var(--spacing-4);
            background-color: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            color: var(--gray-700);
            text-decoration: none;
            transition: all var(--transition-fast);
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .action-btn:hover {
            background-color: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            text-decoration: none;
        }

        .action-btn i {
            font-size: 1.125rem;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            padding: var(--spacing-4);
        }

        .modal-content {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-6);
            border-bottom: 1px solid var(--gray-200);
        }

        .modal-header h3 {
            margin: 0;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
        }

        .modal-close {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--gray-400);
            padding: var(--spacing-2);
            border-radius: var(--radius-sm);
            transition: all var(--transition-fast);
        }

        .modal-close:hover {
            background-color: var(--gray-100);
            color: var(--gray-600);
        }

        .modal-body {
            padding: var(--spacing-6);
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-3);
            padding: var(--spacing-6);
            border-top: 1px solid var(--gray-200);
        }

        /* Data Tables */
        .table-container {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            overflow: hidden;
            margin-bottom: var(--spacing-8);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: var(--spacing-3) var(--spacing-4);
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .data-table th {
            background-color: var(--gray-50);
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.875rem;
        }

        .data-table td {
            color: var(--gray-700);
            font-size: 0.875rem;
        }

        .data-table tr:hover {
            background-color: var(--gray-50);
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* Status Badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.success {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-badge.warning {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-badge.error {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .status-badge.info {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-badge.admin {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-badge.manager {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-badge.accountant {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-badge.auditor {
            background-color: #ede9fe;
            color: #5b21b6;
        }

        .status-badge.employee {
            background-color: #f3f4f6;
            color: #374151;
        }

        /* Alert Messages */
        .alert {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-4);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-4);
            font-size: 0.875rem;
        }

        .alert.success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert.error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .alert.warning {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .alert.info {
            background-color: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .alert-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: var(--spacing-1);
            color: inherit;
            opacity: 0.7;
        }

        .alert-close:hover {
            opacity: 1;
        }

        /* Charts Grid */
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-6);
            margin-top: var(--spacing-4);
        }

        .chart-container {
            background: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-4);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .chart-container h4 {
            margin-bottom: var(--spacing-4);
            color: var(--gray-700);
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
        }

        .chart-container canvas {
            width: 100% !important;
            height: 300px !important;
        }

        /* Utility Classes */
        .hidden {
            display: none !important;
        }

        .text-center {
            text-align: center;
        }

        .mb-4 {
            margin-bottom: var(--spacing-4);
        }

        .mt-4 {
            margin-top: var(--spacing-4);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-visible {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                grid-template-columns: 1fr;
            }

            .charts-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-4);
            }

            .chart-container {
                padding: var(--spacing-3);
            }

            .chart-container canvas {
                height: 250px !important;
            }

            .table-actions {
                flex-wrap: wrap;
                gap: 2px;
            }

            .table-actions .btn {
                padding: 2px 4px;
                min-height: 20px;
                font-size: 10px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .modal-content {
                margin: var(--spacing-2);
                max-width: none;
            }
        }