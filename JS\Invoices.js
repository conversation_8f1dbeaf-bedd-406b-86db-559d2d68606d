/**
 * Invoices.js - Simple and Reliable Invoice Management System
 * Completely rebuilt for maximum compatibility and functionality
 */

class InvoicesManager {
    constructor() {
        this.invoices = [];
        this.customers = [];
        this.nextInvoiceNumber = 1;
        this.isInitialized = false;
        this.currentFilter = 'all';
        this.currentSort = 'date';
        this.sortDirection = 'desc';
        this.useFileStorage = true; // Enable JSON file storage
        this.fileStorageReady = false;

        // Bind methods to ensure proper context
        this.handleCreateInvoice = this.handleCreateInvoice.bind(this);

        console.log('InvoicesManager constructor called');

        // Initialize file storage first, then data
        this.initializeFileStorage();
        this.handleEditInvoice = this.handleEditInvoice.bind(this);
        this.handleCreateCustomer = this.handleCreateCustomer.bind(this);
        this.handleEditCustomer = this.handleEditCustomer.bind(this);
    }

    /**
     * Initialize file storage system
     */
    async initializeFileStorage() {
        try {
            if (window.fileStorageManager) {
                console.log('🔧 Initializing file storage...');
                await window.fileStorageManager.init();
                this.fileStorageReady = true;
                console.log('✅ File storage initialized successfully');

                // Load data from file storage
                await this.loadDataFromFile();

                // Ensure data persistence by saving immediately
                await this.ensureDataPersistence();
            } else {
                console.warn('⚠️ FileStorageManager not available, falling back to localStorage');
                this.useFileStorage = false;
                this.loadData(); // Load from localStorage instead
            }
        } catch (error) {
            console.error('❌ File storage initialization failed:', error);
            this.useFileStorage = false;
            this.loadData(); // Load from localStorage as fallback
        }
    }

    /**
     * Initialize with empty data - all data will be user-created
     */
    initializeEmpty() {
        console.log('=== INITIALIZING EMPTY SYSTEM ===');

        // Start with completely empty data
        this.invoices = [];
        this.customers = [];
        this.nextInvoiceNumber = 1;
        this.isInitialized = true;

        console.log('Empty initialization complete!');
        console.log('System ready for user data creation');

        // Save empty state
        if (this.useFileStorage && this.fileStorageReady) {
            this.saveDataToFile();
        } else {
            this.saveData();
            this.saveCustomers();
        }
    }

    /**
     * Initialize the invoice management system
     */
    init() {
        console.log('Initializing Invoice Manager...');
        if (this.isInitialized) {
            console.log('Invoice Manager already initialized');
            return;
        }

        this.loadData();
        this.setupEventListeners();
        this.updateCustomerDropdowns();
        this.updateInvoiceMetrics();
        this.setupRealTimeUpdates();

        // Ensure customer dropdowns are populated after a short delay
        setTimeout(() => {
            this.updateCustomerDropdowns();
        }, 200);

        this.isInitialized = true;
        console.log('Invoice Manager initialized successfully');
        console.log('Invoices loaded:', this.invoices.length);
        console.log('Customers loaded:', this.customers.length);
    }

    /**
     * Load data from file storage or localStorage
     */
    async loadDataFromFile() {
        try {
            if (this.useFileStorage && this.fileStorageReady) {
                console.log('📂 Loading invoice data from file storage...');

                // Load active invoices from file storage
                const activeInvoices = await window.fileStorageManager.getActiveInvoices();
                this.invoices = Array.isArray(activeInvoices) ? activeInvoices : [];

                // Calculate next invoice number
                if (this.invoices.length > 0) {
                    const maxNumber = Math.max(...this.invoices.map(inv => {
                        const numberPart = inv.number ? inv.number.split('-')[1] : '0';
                        return parseInt(numberPart) || 0;
                    }));
                    this.nextInvoiceNumber = maxNumber + 1;
                } else {
                    this.nextInvoiceNumber = 1;
                }

                console.log('✅ Loaded', this.invoices.length, 'active invoices from file storage');

                // Load customers from file storage
                await this.loadCustomersFromFile();

            } else {
                // Fallback to localStorage
                console.log('📂 Loading data from localStorage...');
                this.loadData();
            }

            this.isInitialized = true;
            console.log('✅ Data loading complete. Invoices:', this.invoices.length, 'Customers:', this.customers.length);

        } catch (error) {
            console.error('❌ Error loading data from file:', error);
            // Fallback to localStorage
            this.loadData();
        }
    }

    /**
     * Load data from localStorage (fallback method)
     */
    loadData() {
        console.log('Loading invoice data from localStorage...');

        // Load invoices
        const savedInvoices = localStorage.getItem('accounting_invoices');
        console.log('Saved invoices from localStorage:', savedInvoices);

        if (savedInvoices) {
            this.invoices = JSON.parse(savedInvoices);
            this.nextInvoiceNumber = Math.max(...this.invoices.map(inv => parseInt(inv.number.split('-')[1])), 0) + 1;
            console.log('Loaded', this.invoices.length, 'invoices from localStorage');
        } else {
            console.log('No saved invoices found, starting with empty data');
            this.initializeEmptyData();
        }

        // Load customers
        this.loadCustomersFromLocalStorage();

        console.log('Data loading complete. Invoices:', this.invoices.length, 'Customers:', this.customers.length);
    }

    /**
     * Load customers from file storage or localStorage
     */
    async loadCustomersFromFile() {
        try {
            if (this.useFileStorage && this.fileStorageReady) {
                console.log('📂 Loading customer data from file storage...');

                // Load active customers from file storage
                const activeCustomers = await window.fileStorageManager.getActiveCustomers();
                this.customers = Array.isArray(activeCustomers) ? activeCustomers : [];

                console.log('✅ Loaded', this.customers.length, 'active customers from file storage');
            } else {
                // Fallback to localStorage
                this.loadCustomersFromLocalStorage();
            }
        } catch (error) {
            console.error('❌ Error loading customers from file:', error);
            // Fallback to localStorage
            this.loadCustomersFromLocalStorage();
        }
    }

    /**
     * Ensure data persistence by saving current state
     */
    async ensureDataPersistence() {
        try {
            if (this.useFileStorage && this.fileStorageReady) {
                // Force save current data to ensure persistence
                await window.fileStorageManager.forceSave();
                console.log('🔒 Data persistence ensured');
            }
        } catch (error) {
            console.error('❌ Failed to ensure data persistence:', error);
        }
    }

    /**
     * Load customers from localStorage (fallback method)
     */
    loadCustomersFromLocalStorage() {
        const savedCustomers = localStorage.getItem('accounting_customers');
        console.log('Saved customers from localStorage:', savedCustomers);

        if (savedCustomers) {
            this.customers = JSON.parse(savedCustomers);
            console.log('Loaded', this.customers.length, 'customers from localStorage');
        } else {
            console.log('No saved customers found, starting with empty data');
            this.initializeEmptyCustomers();
        }
    }

    /**
     * Initialize with empty data - no sample data
     */
    initializeEmptyData() {
        console.log('Initializing empty invoice data...');
        this.invoices = [];
        this.nextInvoiceNumber = 1;
        this.saveData();
    }

    /**
     * Initialize empty customers - no sample data
     */
    initializeEmptyCustomers() {
        console.log('Initializing empty customer data...');
        this.customers = [];
        this.saveCustomers();
    }

    /**
     * Save data to file storage or localStorage
     */
    async saveDataToFile() {
        try {
            if (this.useFileStorage && this.fileStorageReady) {
                // Get all invoices (including inactive ones) and update with current active invoices
                const allInvoices = await window.fileStorageManager.getAllInvoices();

                // Remove old active invoices and add current active invoices
                const inactiveInvoices = allInvoices.filter(inv => inv.isActive === false);
                const updatedInvoices = [...inactiveInvoices, ...this.invoices.map(inv => ({
                    ...inv,
                    isActive: true,
                    updatedAt: new Date().toISOString()
                }))];

                await window.fileStorageManager.saveToFile(updatedInvoices);
                console.log('Invoice data saved to file storage');
            } else {
                // Fallback to localStorage
                this.saveData();
            }

            this.updateInvoiceMetrics();
            this.notifyDashboard();
        } catch (error) {
            console.error('Error saving to file:', error);
            // Fallback to localStorage
            this.saveData();
        }
    }

    /**
     * Save data to localStorage (fallback method)
     */
    saveData() {
        localStorage.setItem('accounting_invoices', JSON.stringify(this.invoices));
        this.updateInvoiceMetrics();
        this.notifyDashboard();
    }

    /**
     * Save customers to file storage or localStorage
     */
    async saveCustomersToFile() {
        try {
            if (this.useFileStorage && this.fileStorageReady) {
                // Get all customers (including inactive ones) and update with current active customers
                const allCustomers = await window.fileStorageManager.getAllCustomers();

                // Remove old active customers and add current active customers
                const inactiveCustomers = allCustomers.filter(cust => cust.isActive === false);
                const updatedCustomers = [...inactiveCustomers, ...this.customers.map(cust => ({
                    ...cust,
                    isActive: true,
                    updatedAt: new Date().toISOString()
                }))];

                await window.fileStorageManager.saveCustomersToFile(updatedCustomers);
                console.log('Customer data saved to file storage');
            } else {
                // Fallback to localStorage
                this.saveCustomers();
            }
        } catch (error) {
            console.error('Error saving customers to file:', error);
            // Fallback to localStorage
            this.saveCustomers();
        }
    }

    /**
     * Save customers to localStorage (fallback method)
     */
    saveCustomers() {
        localStorage.setItem('accounting_customers', JSON.stringify(this.customers));
    }

    /**
     * Create new invoice
     */
    async createInvoice(invoiceData) {
        const amount = parseFloat(invoiceData.amount);
        const tax = amount * 0.15; // 15% tax
        const total = amount + tax;

        const newInvoice = {
            id: Date.now(),
            number: `INV-${String(this.nextInvoiceNumber).padStart(3, '0')}`,
            customerId: parseInt(invoiceData.customerId),
            customerName: this.getCustomerName(invoiceData.customerId),
            amount: amount,
            date: invoiceData.date,
            dueDate: invoiceData.dueDate,
            status: 'pending',
            items: invoiceData.items || [],
            subtotal: amount,
            tax: tax,
            total: total,
            notes: invoiceData.notes || '',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // Add to local array
        this.invoices.unshift(newInvoice);
        this.nextInvoiceNumber++;

        // Save to file storage or localStorage
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                await window.fileStorageManager.addInvoice(newInvoice);
                console.log('Invoice saved to file storage:', newInvoice.number);
            } catch (error) {
                console.error('Error saving to file storage:', error);
                this.saveData(); // Fallback to localStorage
            }
        } else {
            this.saveData();
        }

        this.loadInvoicesTable();
        this.updateCustomerDropdowns();

        // Emit real-time event for dashboard updates
        this.emitInvoiceEvent('invoiceCreated', newInvoice);

        // Notify dashboard
        this.notifyDashboard();

        return newInvoice;
    }

    /**
     * Update existing invoice
     */
    async updateInvoice(invoiceId, updateData) {
        const index = this.invoices.findIndex(inv => inv.id === invoiceId);
        if (index === -1) return false;

        const invoice = this.invoices[index];

        // Update fields
        Object.keys(updateData).forEach(key => {
            if (key !== 'id' && key !== 'number' && key !== 'createdAt') {
                invoice[key] = updateData[key];
            }
        });

        // Recalculate totals if amount changed
        if (updateData.amount) {
            const amount = parseFloat(updateData.amount);
            invoice.amount = amount;
            invoice.subtotal = amount;
            invoice.tax = amount * 0.15;
            invoice.total = amount + invoice.tax;
        }

        invoice.updatedAt = new Date().toISOString();

        // Save to file storage or localStorage
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                await window.fileStorageManager.updateInvoice(invoiceId, invoice);
                console.log('Invoice updated in file storage:', invoice.number);
            } catch (error) {
                console.error('Error updating in file storage:', error);
                this.saveData(); // Fallback to localStorage
            }
        } else {
            this.saveData();
        }

        this.loadInvoicesTable();

        // Emit real-time event for dashboard updates
        this.emitInvoiceEvent('invoiceUpdated', invoice);

        // Notify dashboard
        this.notifyDashboard();

        return invoice;
    }

    /**
     * Delete invoice (soft delete - sets isActive: false)
     */
    async deleteInvoice(invoiceId) {
        const index = this.invoices.findIndex(inv => inv.id === invoiceId);
        if (index === -1) return false;

        const invoiceToDelete = this.invoices[index];

        // Remove from local array (UI will no longer show it)
        this.invoices.splice(index, 1);

        // Soft delete in file storage
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                await window.fileStorageManager.softDeleteInvoice(invoiceId);
                console.log('Invoice soft deleted in file storage:', invoiceToDelete.number);
            } catch (error) {
                console.error('Error soft deleting in file storage:', error);
                // Still save the updated local array to localStorage
                this.saveData();
            }
        } else {
            // For localStorage, we'll just remove it (no soft delete capability)
            this.saveData();
        }

        this.loadInvoicesTable();

        // Emit real-time event for dashboard updates
        this.emitInvoiceEvent('invoiceDeleted', invoiceToDelete);

        // Notify dashboard
        this.notifyDashboard();

        return true;
    }

    /**
     * Update invoice status
     */
    updateInvoiceStatus(invoiceId, newStatus) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return false;

        invoice.status = newStatus;
        invoice.updatedAt = new Date();
        
        if (newStatus === 'paid') {
            invoice.paidDate = new Date();
        }

        this.saveData();
        this.loadInvoicesTable();

        // Emit real-time event for dashboard updates
        this.emitInvoiceEvent('invoiceUpdated', invoice);

        // Notify dashboard
        this.notifyDashboard();

        return true;
    }

    /**
     * Get customer name by ID
     */
    getCustomerName(customerId) {
        const customer = this.customers.find(c => c.id === parseInt(customerId));
        return customer ? customer.name : 'Unknown Customer';
    }

    /**
     * Load and display invoices table - SIMPLE VERSION
     */
    loadInvoicesTable() {
        console.log('=== LOADING INVOICES TABLE ===');

        const tbody = document.getElementById('invoicesTableBody');
        if (!tbody) {
            console.log('❌ Invoices table body not found');
            return;
        }

        console.log('✅ Table body found');
        console.log('📊 Total invoices:', this.invoices.length);

        // Clear table
        tbody.innerHTML = '';

        // If no invoices, show message
        if (this.invoices.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center" style="padding: 20px; color: #666;">
                        No invoices found. Click "Create Invoice" to add your first invoice.
                    </td>
                </tr>
            `;
            console.log('❌ No invoices to display');
            return;
        }

        console.log('✅ Displaying invoices...');

        // Display all invoices (no filtering for now)
        this.invoices.forEach((invoice, index) => {
            console.log(`Adding invoice ${index + 1}:`, invoice.number);

            const row = document.createElement('tr');
            row.innerHTML = `
                <td><strong>${invoice.number}</strong></td>
                <td>${invoice.customerName}</td>
                <td><strong>$${invoice.total.toLocaleString()}</strong></td>
                <td>${new Date(invoice.date).toLocaleDateString()}</td>
                <td>${new Date(invoice.dueDate).toLocaleDateString()}</td>
                <td><span class="status-badge ${invoice.status}">${invoice.status.toUpperCase()}</span></td>
                <td>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-secondary" onclick="alert('View Invoice ${invoice.number}')" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="alert('Edit Invoice ${invoice.number}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="alert('Mark ${invoice.number} as Paid')" title="Mark as Paid">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="alert('Delete Invoice ${invoice.number}')" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        console.log('✅ Table loaded successfully with', this.invoices.length, 'invoices');
    }

    /**
     * Get filtered invoices based on current filter
     */
    getFilteredInvoices() {
        console.log('Getting filtered invoices. Total invoices:', this.invoices.length, 'Filter:', this.currentFilter);

        if (this.currentFilter === 'all') {
            console.log('Returning all invoices:', this.invoices.length);
            return this.invoices;
        }

        const filtered = this.invoices.filter(invoice => invoice.status === this.currentFilter);
        console.log('Filtered invoices:', filtered.length, 'for status:', this.currentFilter);
        return filtered;
    }

    /**
     * Get sorted invoices
     */
    getSortedInvoices(invoices) {
        return invoices.sort((a, b) => {
            let aValue = a[this.currentSort];
            let bValue = b[this.currentSort];

            // Handle date sorting
            if (this.currentSort === 'date' || this.currentSort === 'dueDate') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            }

            // Handle numeric sorting
            if (this.currentSort === 'amount' || this.currentSort === 'total') {
                aValue = parseFloat(aValue);
                bValue = parseFloat(bValue);
            }

            if (this.sortDirection === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }

    /**
     * Check if invoice is overdue
     */
    isOverdue(invoice) {
        if (invoice.status === 'paid') return false;
        return new Date(invoice.dueDate) < new Date();
    }

    /**
     * Update invoice metrics
     */
    updateInvoiceMetrics() {
        const totalInvoices = this.invoices.length;
        const paidInvoices = this.invoices.filter(inv => inv.status === 'paid').length;
        const pendingInvoices = this.invoices.filter(inv => inv.status === 'pending').length;
        const overdueInvoices = this.invoices.filter(inv => this.isOverdue(inv)).length;
        
        const totalRevenue = this.invoices
            .filter(inv => inv.status === 'paid')
            .reduce((sum, inv) => sum + inv.total, 0);
        
        const pendingAmount = this.invoices
            .filter(inv => inv.status === 'pending')
            .reduce((sum, inv) => sum + inv.total, 0);

        // Update metric cards if they exist
        this.updateMetricCard('totalInvoices', totalInvoices);
        this.updateMetricCard('paidInvoices', paidInvoices);
        this.updateMetricCard('pendingInvoices', pendingInvoices);
        this.updateMetricCard('overdueInvoices', overdueInvoices);
        this.updateMetricCard('totalRevenue', `$${totalRevenue.toLocaleString()}`);
        this.updateMetricCard('pendingAmount', `$${pendingAmount.toLocaleString()}`);
    }

    /**
     * Update metric card
     */
    updateMetricCard(metricId, value) {
        const element = document.getElementById(metricId);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * Update invoice count display
     */
    updateInvoiceCount(count) {
        const countElement = document.getElementById('invoiceCount');
        if (countElement) {
            countElement.textContent = `${count} invoice${count !== 1 ? 's' : ''}`;
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // Sort headers
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                this.setSorting(e.target.dataset.sort);
            });
        });

        // Search input
        const searchInput = document.getElementById('invoiceSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchInvoices(e.target.value);
            });
        }

        // Create invoice form
        const createForm = document.getElementById('createInvoiceForm');
        if (createForm) {
            createForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCreateInvoice(e);
            });
        } else {
            // Try again after a short delay if form not found
            setTimeout(() => {
                const createFormDelayed = document.getElementById('createInvoiceForm');
                if (createFormDelayed) {
                    createFormDelayed.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleCreateInvoice(e);
                    });
                }
            }, 100);
        }

        // Edit invoice form
        const editForm = document.getElementById('editInvoiceForm');
        if (editForm) {
            editForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditInvoice(e);
            });
        }

        // Create customer form
        const createCustomerForm = document.getElementById('createCustomerForm');
        if (createCustomerForm) {
            createCustomerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCreateCustomer(e);
            });
        }

        // Edit customer form
        const editCustomerForm = document.getElementById('editCustomerForm');
        if (editCustomerForm) {
            editCustomerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditCustomer(e);
            });
        }
    }

    /**
     * Set current filter
     */
    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        this.loadInvoicesTable();
    }

    /**
     * Set sorting
     */
    setSorting(sortField) {
        if (this.currentSort === sortField) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort = sortField;
            this.sortDirection = 'desc';
        }
        
        this.loadInvoicesTable();
    }

    /**
     * Search invoices
     */
    searchInvoices(query) {
        const tbody = document.getElementById('invoicesTableBody');
        if (!tbody) return;

        const filteredInvoices = this.invoices.filter(invoice => 
            invoice.number.toLowerCase().includes(query.toLowerCase()) ||
            invoice.customerName.toLowerCase().includes(query.toLowerCase()) ||
            invoice.status.toLowerCase().includes(query.toLowerCase())
        );

        this.displaySearchResults(filteredInvoices);
    }

    /**
     * Display search results
     */
    displaySearchResults(invoices) {
        const tbody = document.getElementById('invoicesTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';
        
        invoices.forEach(invoice => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${invoice.number}</td>
                <td>${invoice.customerName}</td>
                <td>$${invoice.total.toLocaleString()}</td>
                <td>${new Date(invoice.date).toLocaleDateString()}</td>
                <td>${new Date(invoice.dueDate).toLocaleDateString()}</td>
                <td><span class="status-badge ${invoice.status}">${invoice.status}</span></td>
                <td>
                    ${this.isOverdue(invoice) ? '<span class="overdue-badge">Overdue</span>' : ''}
                </td>
                <td>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-secondary" onclick="invoicesManager.viewInvoice(${invoice.id})" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="invoicesManager.editInvoice(${invoice.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="invoicesManager.markAsPaid(${invoice.id})" title="Mark as Paid">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="invoicesManager.deleteInvoiceConfirm(${invoice.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        this.updateInvoiceCount(invoices.length);
    }

    /**
     * Handle create invoice form submission
     */
    handleCreateInvoice(event) {
        console.log('=== INVOICE CREATION STARTED ===');
        event.preventDefault();

        const formData = new FormData(event.target);
        const invoiceData = Object.fromEntries(formData);

        console.log('Form data received:', invoiceData);
        console.log('Available customers:', this.customers.length);
        console.log('Customer list:', this.customers);

        // Check if we have customers
        if (this.customers.length === 0) {
            console.error('No customers available');
            this.showAlert('No customers available. Please add a customer first.', 'error');
            return;
        }

        // Enhanced validation with detailed logging
        const validationErrors = [];

        if (!invoiceData.customerId) {
            validationErrors.push('Customer is required');
        }
        if (!invoiceData.amount) {
            validationErrors.push('Amount is required');
        }
        if (!invoiceData.invoiceDate) {
            validationErrors.push('Invoice date is required');
        }
        if (!invoiceData.dueDate) {
            validationErrors.push('Due date is required');
        }

        if (validationErrors.length > 0) {
            console.error('Validation failed:', validationErrors);
            this.showAlert('Please fill in all required fields: ' + validationErrors.join(', '), 'error');
            return;
        }

        const amount = parseFloat(invoiceData.amount);
        if (isNaN(amount) || amount <= 0) {
            console.error('Invalid amount:', invoiceData.amount);
            this.showAlert('Invoice amount must be a valid number greater than zero.', 'error');
            return;
        }

        // Verify customer exists
        const customer = this.customers.find(c => c.id == invoiceData.customerId);
        if (!customer) {
            console.error('Customer not found:', invoiceData.customerId);
            this.showAlert('Selected customer not found. Please select a valid customer.', 'error');
            return;
        }

        // Process the data
        const processedData = {
            customerId: parseInt(invoiceData.customerId),
            amount: amount,
            date: invoiceData.invoiceDate,
            dueDate: invoiceData.dueDate,
            notes: invoiceData.notes || ''
        };

        console.log('Processed data:', processedData);
        console.log('Selected customer:', customer);

        try {
            const newInvoice = this.createInvoice(processedData);
            console.log('Invoice created successfully:', newInvoice);
            this.showAlert(`Invoice ${newInvoice.number} created successfully for ${customer.name}!`, 'success');

            // Reset form and close modal
            event.target.reset();
            this.hideModal('createInvoiceModal');

            // Refresh the invoices table if we're on the invoices page
            if (window.currentPage === 'invoices') {
                this.loadInvoicesTable();
            }
        } catch (error) {
            console.error('Error creating invoice:', error);
            this.showAlert('Failed to create invoice: ' + error.message, 'error');
        }
    }

    /**
     * Setup real-time updates
     */
    setupRealTimeUpdates() {
        // Auto-save every 30 seconds
        setInterval(() => {
            this.saveData();
        }, 30000);

        // Check for overdue invoices every hour
        setInterval(() => {
            this.checkOverdueInvoices();
        }, 3600000);
    }

    /**
     * Check for overdue invoices and notify
     */
    checkOverdueInvoices() {
        const overdueInvoices = this.invoices.filter(inv => this.isOverdue(inv));
        if (overdueInvoices.length > 0) {
            console.log(`${overdueInvoices.length} overdue invoices found`);
            // Could trigger notifications here
        }
    }

    /**
     * View invoice details
     */
    viewInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return;

        // Implementation for viewing invoice details
        console.log('Viewing invoice:', invoice);
        this.showAlert('Invoice details view would open here.', 'info');
    }

    /**
     * Edit invoice
     */
    editInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return;

        // Populate edit form
        this.populateEditForm(invoice);
        this.showModal('editInvoiceModal');
    }

    /**
     * Populate edit form with invoice data
     */
    populateEditForm(invoice) {
        document.getElementById('editInvoiceId').value = invoice.id;
        document.getElementById('editInvoiceCustomer').value = invoice.customerId;
        document.getElementById('editInvoiceAmount').value = invoice.amount;
        document.getElementById('editInvoiceDate').value = invoice.date;
        document.getElementById('editInvoiceDueDate').value = invoice.dueDate;
        document.getElementById('editInvoiceStatus').value = invoice.status;
        document.getElementById('editInvoiceNotes').value = invoice.notes || '';
    }

    /**
     * Handle edit invoice form submission
     */
    handleEditInvoice(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const invoiceData = Object.fromEntries(formData);

        const invoiceId = parseInt(invoiceData.invoiceId);

        // Validation
        if (!invoiceData.customerId || !invoiceData.amount || !invoiceData.invoiceDate || !invoiceData.dueDate) {
            this.showAlert('Please fill in all required fields.', 'error');
            return;
        }

        if (parseFloat(invoiceData.amount) <= 0) {
            this.showAlert('Invoice amount must be greater than zero.', 'error');
            return;
        }

        // Update invoice
        const updateData = {
            customerId: parseInt(invoiceData.customerId),
            customerName: this.getCustomerName(invoiceData.customerId),
            amount: parseFloat(invoiceData.amount),
            date: invoiceData.invoiceDate,
            dueDate: invoiceData.dueDate,
            status: invoiceData.status,
            notes: invoiceData.notes || ''
        };

        const updatedInvoice = this.updateInvoice(invoiceId, updateData);
        if (updatedInvoice) {
            this.showAlert(`Invoice ${updatedInvoice.number} updated successfully!`, 'success');
            this.hideModal('editInvoiceModal');
        } else {
            this.showAlert('Failed to update invoice.', 'error');
        }
    }

    /**
     * Mark invoice as paid
     */
    markAsPaid(invoiceId) {
        if (this.updateInvoiceStatus(invoiceId, 'paid')) {
            this.showAlert('Invoice marked as paid successfully!', 'success');
        }
    }

    /**
     * Send invoice
     */
    sendInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return;

        // Implementation for sending invoice
        console.log('Sending invoice:', invoice);
        this.showAlert('Invoice sent successfully!', 'success');
    }

    /**
     * Delete invoice with confirmation
     */
    deleteInvoiceConfirm(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (!invoice) return;

        if (confirm(`Are you sure you want to delete invoice ${invoice.number}? This action cannot be undone.`)) {
            if (this.deleteInvoice(invoiceId)) {
                this.showAlert('Invoice deleted successfully!', 'success');
            } else {
                this.showAlert('Failed to delete invoice.', 'error');
            }
        }
    }

    /**
     * Get all invoices (for dashboard integration)
     */
    getAllInvoices() {
        return this.invoices;
    }

    /**
     * Show alert message
     */
    showAlert(message, type) {
        // Implementation depends on your alert system
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // If you have a global showAlert function, use it
        if (window.showAlert) {
            window.showAlert(message, type);
        }
    }

    /**
     * Show modal
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    /**
     * Hide modal
     */
    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    /**
     * Customer Management Functions
     */

    /**
     * Create new customer
     */
    async createCustomer(customerData) {
        const newCustomer = {
            id: Math.max(...this.customers.map(c => c.id), 0) + 1,
            name: customerData.name,
            email: customerData.email,
            phone: customerData.phone || '',
            address: customerData.address || '',
            company: customerData.company || '',
            notes: customerData.notes || '',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // Add to local array
        this.customers.push(newCustomer);

        // Save to file storage or localStorage
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                await window.fileStorageManager.addCustomer(newCustomer);
                console.log('Customer saved to file storage:', newCustomer.name);
            } catch (error) {
                console.error('Error saving customer to file storage:', error);
                this.saveCustomers(); // Fallback to localStorage
            }
        } else {
            this.saveCustomers();
        }

        this.loadCustomersTable();
        this.updateCustomerDropdowns(); // Update dropdowns when customer is added

        return newCustomer;
    }

    /**
     * Update existing customer
     */
    async updateCustomer(customerId, updateData) {
        const index = this.customers.findIndex(c => c.id === customerId);
        if (index === -1) return false;

        const customer = this.customers[index];

        // Update fields
        Object.keys(updateData).forEach(key => {
            if (key !== 'id' && key !== 'createdAt') {
                customer[key] = updateData[key];
            }
        });

        customer.updatedAt = new Date().toISOString();

        // Save to file storage or localStorage
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                await window.fileStorageManager.updateCustomer(customerId, customer);
                console.log('Customer updated in file storage:', customer.name);
            } catch (error) {
                console.error('Error updating customer in file storage:', error);
                this.saveCustomers(); // Fallback to localStorage
            }
        } else {
            this.saveCustomers();
        }

        this.loadCustomersTable();
        this.updateCustomerDropdowns(); // Update dropdowns when customer is updated

        return customer;
    }

    /**
     * Delete customer (soft delete)
     */
    async deleteCustomer(customerId) {
        // Check if customer has invoices
        const hasInvoices = this.invoices.some(inv => inv.customerId === customerId);
        if (hasInvoices) {
            this.showAlert('Cannot delete customer with existing invoices.', 'error');
            return false;
        }

        const index = this.customers.findIndex(c => c.id === customerId);
        if (index === -1) return false;

        const customerToDelete = this.customers[index];

        // Remove from local array (UI will no longer show it)
        this.customers.splice(index, 1);

        // Soft delete in file storage
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                await window.fileStorageManager.softDeleteCustomer(customerId);
                console.log('Customer soft deleted in file storage:', customerToDelete.name);
            } catch (error) {
                console.error('Error soft deleting customer in file storage:', error);
                // Still save the updated local array to localStorage
                this.saveCustomers();
            }
        } else {
            // For localStorage, we'll just remove it (no soft delete capability)
            this.saveCustomers();
        }

        this.loadCustomersTable();
        this.updateCustomerDropdowns(); // Update dropdowns when customer is deleted

        return true;
    }

    /**
     * Load and display customers table
     */
    loadCustomersTable() {
        const tbody = document.getElementById('customersTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.customers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">No customers found</td>
                </tr>
            `;
            return;
        }

        this.customers.forEach(customer => {
            const invoiceCount = this.invoices.filter(inv => inv.customerId === customer.id).length;
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${customer.email}</td>
                <td>${customer.phone}</td>
                <td>${customer.address}</td>
                <td>${invoiceCount}</td>
                <td>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-secondary" onclick="invoicesManager.editCustomer(${customer.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="invoicesManager.deleteCustomerConfirm(${customer.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    /**
     * Handle create customer form submission
     */
    handleCreateCustomer(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const customerData = Object.fromEntries(formData);

        // Validation
        if (!customerData.name || !customerData.email) {
            this.showAlert('Please fill in all required fields.', 'error');
            return;
        }

        // Check if email already exists
        if (this.customers.some(c => c.email === customerData.email)) {
            this.showAlert('Customer with this email already exists.', 'error');
            return;
        }

        const newCustomer = this.createCustomer(customerData);
        this.showAlert(`Customer ${newCustomer.name} created successfully!`, 'success');

        // Reset form and close modal
        event.target.reset();
        this.hideModal('createCustomerModal');
    }

    /**
     * Edit customer
     */
    editCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        // Populate edit form
        this.populateCustomerEditForm(customer);
        this.showModal('editCustomerModal');
    }

    /**
     * Populate customer edit form
     */
    populateCustomerEditForm(customer) {
        document.getElementById('editCustomerId').value = customer.id;
        document.getElementById('editCustomerName').value = customer.name;
        document.getElementById('editCustomerEmail').value = customer.email;
        document.getElementById('editCustomerPhone').value = customer.phone || '';
        document.getElementById('editCustomerAddress').value = customer.address || '';
    }

    /**
     * Handle edit customer form submission
     */
    handleEditCustomer(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const customerData = Object.fromEntries(formData);

        const customerId = parseInt(customerData.customerId);

        // Validation
        if (!customerData.name || !customerData.email) {
            this.showAlert('Please fill in all required fields.', 'error');
            return;
        }

        // Check if email already exists (excluding current customer)
        if (this.customers.some(c => c.email === customerData.email && c.id !== customerId)) {
            this.showAlert('Customer with this email already exists.', 'error');
            return;
        }

        // Update customer
        const updateData = {
            name: customerData.name,
            email: customerData.email,
            phone: customerData.phone || '',
            address: customerData.address || ''
        };

        const updatedCustomer = this.updateCustomer(customerId, updateData);
        if (updatedCustomer) {
            this.showAlert(`Customer ${updatedCustomer.name} updated successfully!`, 'success');
            this.hideModal('editCustomerModal');
            // Update invoice table if customer name changed
            this.loadInvoicesTable();
        } else {
            this.showAlert('Failed to update customer.', 'error');
        }
    }

    /**
     * Delete customer with confirmation
     */
    deleteCustomerConfirm(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        if (confirm(`Are you sure you want to delete customer ${customer.name}? This action cannot be undone.`)) {
            if (this.deleteCustomer(customerId)) {
                this.showAlert('Customer deleted successfully!', 'success');
            }
        }
    }

    /**
     * Update customer dropdowns in all forms
     */
    updateCustomerDropdowns() {
        console.log('=== UPDATING CUSTOMER DROPDOWNS ===');
        console.log('Available customers:', this.customers.length);

        const dropdowns = [
            document.getElementById('invoiceCustomer'),
            document.getElementById('editInvoiceCustomer')
        ];

        dropdowns.forEach((dropdown, index) => {
            if (dropdown) {
                console.log(`Updating dropdown ${index + 1}:`, dropdown.id);

                // Save current value
                const currentValue = dropdown.value;

                // Clear existing options except the first one
                dropdown.innerHTML = '<option value="">Select Customer</option>';

                // Add customers from the customer management
                this.customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.id;
                    option.textContent = customer.name;
                    dropdown.appendChild(option);
                    console.log(`Added customer option: ${customer.name} (ID: ${customer.id})`);
                });

                // Restore previous value if it still exists
                if (currentValue && this.customers.find(c => c.id == currentValue)) {
                    dropdown.value = currentValue;
                }

                console.log(`Dropdown ${dropdown.id} now has ${dropdown.options.length} options`);
            } else {
                console.log(`Dropdown ${index + 1} not found`);
            }
        });

        console.log('Customer dropdowns update complete');
    }

    /**
     * Notify dashboard of changes
     */
    notifyDashboard() {
        if (window.dashboardManager) {
            window.dashboardManager.loadDashboardMetrics();
        }
    }

    /**
     * Emit real-time events for dashboard updates
     */
    emitInvoiceEvent(eventType, invoiceData) {
        const event = new CustomEvent(eventType, {
            detail: { invoice: invoiceData, timestamp: new Date() }
        });
        document.dispatchEvent(event);
        console.log(`Invoice event emitted: ${eventType}`, invoiceData);
    }

    /**
     * Real-time Data Management Functions
     */

    /**
     * Clear all invoices
     */
    clearAllInvoices() {
        if (confirm('Are you sure you want to delete ALL invoices? This action cannot be undone.')) {
            this.invoices = [];
            this.nextInvoiceNumber = 1;
            this.saveData();
            this.loadInvoicesTable();
            this.showAlert('All invoices have been deleted.', 'success');
            console.log('All invoices cleared');
        }
    }

    /**
     * Clear all customers
     */
    clearAllCustomers() {
        // Check if any customers have invoices
        const customersWithInvoices = this.customers.filter(customer =>
            this.invoices.some(invoice => invoice.customerId === customer.id)
        );

        if (customersWithInvoices.length > 0) {
            this.showAlert(`Cannot delete customers. ${customersWithInvoices.length} customers have existing invoices.`, 'error');
            return;
        }

        if (confirm('Are you sure you want to delete ALL customers? This action cannot be undone.')) {
            this.customers = [];
            this.saveCustomers();
            this.loadCustomersTable();
            this.updateCustomerDropdowns();
            this.showAlert('All customers have been deleted.', 'success');
            console.log('All customers cleared');
        }
    }

    /**
     * Export all data
     */
    exportData() {
        const data = {
            invoices: this.invoices,
            customers: this.customers,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };

        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `invoice-data-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.showAlert('Data exported successfully!', 'success');
        console.log('Data exported:', data);
    }

    /**
     * Import data from file
     */
    importData(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedData = JSON.parse(e.target.result);

                if (!importedData.invoices && !importedData.customers) {
                    this.showAlert('Invalid data format. Please select a valid export file.', 'error');
                    return;
                }

                // Confirm import
                const invoiceCount = importedData.invoices ? importedData.invoices.length : 0;
                const customerCount = importedData.customers ? importedData.customers.length : 0;

                if (confirm(`Import ${invoiceCount} invoices and ${customerCount} customers? This will replace all existing data.`)) {
                    // Import customers first
                    if (importedData.customers) {
                        this.customers = importedData.customers;
                        this.saveCustomers();
                    }

                    // Import invoices
                    if (importedData.invoices) {
                        this.invoices = importedData.invoices;
                        this.nextInvoiceNumber = Math.max(...this.invoices.map(inv => parseInt(inv.number.split('-')[1])), 0) + 1;
                        this.saveData();
                    }

                    // Refresh displays
                    this.loadInvoicesTable();
                    this.loadCustomersTable();
                    this.updateCustomerDropdowns();

                    this.showAlert(`Data imported successfully! ${invoiceCount} invoices and ${customerCount} customers.`, 'success');
                    console.log('Data imported successfully');
                }
            } catch (error) {
                console.error('Import error:', error);
                this.showAlert('Error importing data. Please check the file format.', 'error');
            }
        };
        reader.readAsText(file);
    }

    /**
     * Get data statistics
     */
    getDataStats() {
        const stats = {
            totalInvoices: this.invoices.length,
            totalCustomers: this.customers.length,
            paidInvoices: this.invoices.filter(inv => inv.status === 'paid').length,
            pendingInvoices: this.invoices.filter(inv => inv.status === 'pending').length,
            overdueInvoices: this.invoices.filter(inv => this.isOverdue(inv)).length,
            totalRevenue: this.invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0),
            pendingAmount: this.invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0)
        };

        console.log('Data Statistics:', stats);
        return stats;
    }

    /**
     * Get soft deleted invoices
     */
    async getSoftDeletedInvoices() {
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                return await window.fileStorageManager.getInactiveInvoices();
            } catch (error) {
                console.error('Error getting soft deleted invoices:', error);
                return [];
            }
        }
        return []; // localStorage doesn't support soft delete
    }

    /**
     * Get soft deleted customers
     */
    async getSoftDeletedCustomers() {
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                return await window.fileStorageManager.getInactiveCustomers();
            } catch (error) {
                console.error('Error getting soft deleted customers:', error);
                return [];
            }
        }
        return []; // localStorage doesn't support soft delete
    }

    /**
     * Restore soft deleted invoice
     */
    async restoreInvoice(invoiceId) {
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                const restoredInvoice = await window.fileStorageManager.restoreInvoice(invoiceId);

                // Add back to local array
                this.invoices.unshift(restoredInvoice);
                this.loadInvoicesTable();

                this.showAlert(`Invoice ${restoredInvoice.number} restored successfully!`, 'success');
                console.log('Invoice restored:', restoredInvoice.number);
                return restoredInvoice;
            } catch (error) {
                console.error('Error restoring invoice:', error);
                this.showAlert('Failed to restore invoice.', 'error');
                return null;
            }
        } else {
            this.showAlert('Restore feature requires file storage.', 'error');
            return null;
        }
    }

    /**
     * Permanently delete invoice (hard delete)
     */
    async permanentlyDeleteInvoice(invoiceId) {
        if (confirm('Are you sure you want to permanently delete this invoice? This action cannot be undone.')) {
            if (this.useFileStorage && this.fileStorageReady) {
                try {
                    const deletedInvoice = await window.fileStorageManager.hardDeleteInvoice(invoiceId);
                    this.showAlert(`Invoice ${deletedInvoice.number} permanently deleted.`, 'success');
                    console.log('Invoice permanently deleted:', deletedInvoice.number);
                    return deletedInvoice;
                } catch (error) {
                    console.error('Error permanently deleting invoice:', error);
                    this.showAlert('Failed to permanently delete invoice.', 'error');
                    return null;
                }
            } else {
                this.showAlert('Permanent delete feature requires file storage.', 'error');
                return null;
            }
        }
    }

    /**
     * Restore soft deleted customer
     */
    async restoreCustomer(customerId) {
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                const restoredCustomer = await window.fileStorageManager.restoreCustomer(customerId);

                // Add back to local array
                this.customers.push(restoredCustomer);
                this.loadCustomersTable();
                this.updateCustomerDropdowns();

                this.showAlert(`Customer ${restoredCustomer.name} restored successfully!`, 'success');
                console.log('Customer restored:', restoredCustomer.name);
                return restoredCustomer;
            } catch (error) {
                console.error('Error restoring customer:', error);
                this.showAlert('Failed to restore customer.', 'error');
                return null;
            }
        } else {
            this.showAlert('Restore feature requires file storage.', 'error');
            return null;
        }
    }

    /**
     * Permanently delete customer (hard delete)
     */
    async permanentlyDeleteCustomer(customerId) {
        if (confirm('Are you sure you want to permanently delete this customer? This action cannot be undone.')) {
            if (this.useFileStorage && this.fileStorageReady) {
                try {
                    const deletedCustomer = await window.fileStorageManager.hardDeleteCustomer(customerId);
                    this.showAlert(`Customer ${deletedCustomer.name} permanently deleted.`, 'success');
                    console.log('Customer permanently deleted:', deletedCustomer.name);
                    return deletedCustomer;
                } catch (error) {
                    console.error('Error permanently deleting customer:', error);
                    this.showAlert('Failed to permanently delete customer.', 'error');
                    return null;
                }
            } else {
                this.showAlert('Permanent delete feature requires file storage.', 'error');
                return null;
            }
        }
    }

    /**
     * Get file storage statistics
     */
    async getStorageStats() {
        if (this.useFileStorage && this.fileStorageReady) {
            try {
                return await window.fileStorageManager.getStorageStats();
            } catch (error) {
                console.error('Error getting storage stats:', error);
                return null;
            }
        }
        return null;
    }

    /**
     * Reset system to empty state
     */
    async resetSystem() {
        if (confirm('Are you sure you want to reset the entire system? This will delete ALL data and cannot be undone.')) {
            if (confirm('This is your final warning. ALL invoices and customers will be permanently deleted. Continue?')) {
                this.invoices = [];
                this.customers = [];
                this.nextInvoiceNumber = 1;

                // Clear file storage if available
                if (this.useFileStorage && this.fileStorageReady) {
                    try {
                        await window.fileStorageManager.clearAllData();
                        console.log('File storage cleared');
                    } catch (error) {
                        console.error('Error clearing file storage:', error);
                    }
                }

                this.saveData();
                this.saveCustomers();

                this.loadInvoicesTable();
                this.loadCustomersTable();
                this.updateCustomerDropdowns();

                this.showAlert('System has been reset to empty state.', 'success');
                console.log('System reset to empty state');
            }
        }
    }
}

// Create global instance
window.invoicesManager = new InvoicesManager();

// Global function for form submission (backup)
window.handleCreateInvoice = function(event) {
    if (window.invoicesManager) {
        window.invoicesManager.handleCreateInvoice(event);
    }
};

// Force initialization function
window.initializeInvoicesManager = function() {
    console.log('=== FORCE INITIALIZING INVOICES MANAGER ===');

    if (!window.invoicesManager) {
        console.error('InvoicesManager not found!');
        return false;
    }

    // Reset the manager
    window.invoicesManager.isInitialized = false;
    window.invoicesManager.invoices = [];
    window.invoicesManager.customers = [];

    // Force initialization
    window.invoicesManager.init();

    console.log('Force initialization complete');
    console.log('Invoices:', window.invoicesManager.invoices.length);
    console.log('Customers:', window.invoicesManager.customers.length);

    return true;
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM loaded, initializing InvoicesManager...');
        setTimeout(() => {
            window.invoicesManager.init();
        }, 200);
    });
} else {
    console.log('DOM already loaded, initializing InvoicesManager...');
    // Delay initialization slightly to ensure everything is ready
    setTimeout(() => {
        window.invoicesManager.init();
    }, 200);
}

// Additional initialization check
window.ensureInvoicesManagerReady = function() {
    if (!window.invoicesManager) {
        console.error('InvoicesManager not found!');
        return false;
    }

    if (!window.invoicesManager.isInitialized) {
        console.log('InvoicesManager not initialized, initializing now...');
        window.invoicesManager.init();
    }

    return true;
};
