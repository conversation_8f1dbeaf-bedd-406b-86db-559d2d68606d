<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Storage System Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th { background: #f8f9fa; }
        
        .status-active { color: #28a745; font-weight: bold; }
        .status-inactive { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 JSON File Storage System Test</h1>
        
        <div class="test-section">
            <h3>📊 System Status</h3>
            <button class="btn-info" onclick="checkSystemStatus()">Check System Status</button>
            <button class="btn-primary" onclick="initializeSystem()">Initialize System</button>
            <button class="btn-secondary" onclick="getStorageStats()">Storage Statistics</button>
            <div id="systemStatus"></div>
        </div>

        <div class="test-section">
            <h3>📝 Invoice Operations</h3>
            <button class="btn-success" onclick="createTestInvoice()">Create Test Invoice</button>
            <button class="btn-warning" onclick="updateLastInvoice()">Update Last Invoice</button>
            <button class="btn-danger" onclick="deleteLastInvoice()">Soft Delete Last Invoice</button>
            <button class="btn-info" onclick="loadActiveInvoices()">Load Active Invoices</button>
            <div id="invoiceResults"></div>
        </div>

        <div class="test-section">
            <h3>👥 Customer Operations</h3>
            <button class="btn-success" onclick="createTestCustomer()">Create Test Customer</button>
            <button class="btn-warning" onclick="updateLastCustomer()">Update Last Customer</button>
            <button class="btn-danger" onclick="deleteLastCustomer()">Soft Delete Last Customer</button>
            <button class="btn-info" onclick="loadActiveCustomers()">Load Active Customers</button>
            <div id="customerResults"></div>
        </div>

        <div class="test-section">
            <h3>🗑️ Soft Delete Management</h3>
            <h4>📄 Invoices</h4>
            <button class="btn-info" onclick="loadSoftDeletedInvoices()">View Soft Deleted Invoices</button>
            <button class="btn-success" onclick="restoreLastDeletedInvoice()">Restore Last Deleted Invoice</button>
            <button class="btn-danger" onclick="permanentlyDeleteLastInvoice()">Permanently Delete Last Invoice</button>

            <h4>👥 Customers</h4>
            <button class="btn-info" onclick="loadSoftDeletedCustomers()">View Soft Deleted Customers</button>
            <button class="btn-success" onclick="restoreLastDeletedCustomer()">Restore Last Deleted Customer</button>
            <button class="btn-danger" onclick="permanentlyDeleteLastCustomer()">Permanently Delete Last Customer</button>

            <div id="softDeleteResults"></div>
        </div>

        <div class="test-section">
            <h3>💾 Data Management</h3>
            <button class="btn-primary" onclick="exportData()">Export Data</button>
            <button class="btn-warning" onclick="createBackup()">Create Backup</button>
            <button class="btn-info" onclick="clearAllData()">Clear All Data</button>
            <div id="dataManagementResults"></div>
        </div>

        <div class="test-section">
            <h3>📋 Current Data</h3>
            <button class="btn-secondary" onclick="refreshDataDisplay()">Refresh Display</button>
            <div id="dataDisplay"></div>
        </div>
    </div>

    <script src="JS/FileStorageManager.js"></script>
    <script>
        let testInvoiceCounter = 1;
        let testCustomerCounter = 1;

        function log(message, type = 'info') {
            console.log(message);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = typeof message === 'object' ? JSON.stringify(message, null, 2) : message;
            return resultDiv;
        }

        async function checkSystemStatus() {
            const container = document.getElementById('systemStatus');
            container.innerHTML = '';

            try {
                const status = {
                    fileStorageManager: !!window.fileStorageManager,
                    initialized: window.fileStorageManager?.isInitialized || false,
                    cache: window.fileStorageManager?.cache?.length || 0,
                    lastModified: window.fileStorageManager?.lastModified || 'Never'
                };

                container.appendChild(log('System Status: ' + JSON.stringify(status, null, 2), 'info'));
            } catch (error) {
                container.appendChild(log('Error checking status: ' + error.message, 'error'));
            }
        }

        async function initializeSystem() {
            const container = document.getElementById('systemStatus');
            
            try {
                if (!window.fileStorageManager) {
                    container.appendChild(log('Creating FileStorageManager...', 'info'));
                    window.fileStorageManager = new FileStorageManager();
                }

                container.appendChild(log('Initializing file storage...', 'info'));
                await window.fileStorageManager.init();
                container.appendChild(log('File storage initialized successfully!', 'success'));
                
                await checkSystemStatus();
            } catch (error) {
                container.appendChild(log('Initialization failed: ' + error.message, 'error'));
            }
        }

        async function getStorageStats() {
            const container = document.getElementById('systemStatus');
            
            try {
                const stats = await window.fileStorageManager.getStorageStats();
                container.appendChild(log('Storage Statistics: ' + JSON.stringify(stats, null, 2), 'info'));
            } catch (error) {
                container.appendChild(log('Error getting stats: ' + error.message, 'error'));
            }
        }

        async function createTestInvoice() {
            const container = document.getElementById('invoiceResults');
            
            try {
                const testInvoice = {
                    number: `TEST-${String(testInvoiceCounter).padStart(3, '0')}`,
                    customerId: 1,
                    customerName: `Test Customer ${testInvoiceCounter}`,
                    amount: Math.floor(Math.random() * 5000) + 100,
                    date: new Date().toISOString().split('T')[0],
                    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    status: 'pending',
                    notes: `Test invoice #${testInvoiceCounter}`
                };

                const result = await window.fileStorageManager.addInvoice(testInvoice);
                container.appendChild(log('Invoice created: ' + JSON.stringify(result, null, 2), 'success'));
                testInvoiceCounter++;
                
                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error creating invoice: ' + error.message, 'error'));
            }
        }

        async function updateLastInvoice() {
            const container = document.getElementById('invoiceResults');
            
            try {
                const invoices = await window.fileStorageManager.getActiveInvoices();
                if (invoices.length === 0) {
                    container.appendChild(log('No invoices to update', 'error'));
                    return;
                }

                const lastInvoice = invoices[0];
                const updateData = {
                    amount: lastInvoice.amount + 100,
                    notes: lastInvoice.notes + ' (UPDATED)',
                    status: lastInvoice.status === 'pending' ? 'paid' : 'pending'
                };

                const result = await window.fileStorageManager.updateInvoice(lastInvoice.id, updateData);
                container.appendChild(log('Invoice updated: ' + JSON.stringify(result, null, 2), 'success'));
                
                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error updating invoice: ' + error.message, 'error'));
            }
        }

        async function deleteLastInvoice() {
            const container = document.getElementById('invoiceResults');
            
            try {
                const invoices = await window.fileStorageManager.getActiveInvoices();
                if (invoices.length === 0) {
                    container.appendChild(log('No invoices to delete', 'error'));
                    return;
                }

                const lastInvoice = invoices[0];
                const result = await window.fileStorageManager.softDeleteInvoice(lastInvoice.id);
                container.appendChild(log('Invoice soft deleted: ' + JSON.stringify(result, null, 2), 'success'));
                
                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error deleting invoice: ' + error.message, 'error'));
            }
        }

        async function loadActiveInvoices() {
            const container = document.getElementById('invoiceResults');

            try {
                const invoices = await window.fileStorageManager.getActiveInvoices();
                container.appendChild(log(`Loaded ${invoices.length} active invoices`, 'info'));

                if (invoices.length > 0) {
                    container.appendChild(log('Sample: ' + JSON.stringify(invoices[0], null, 2), 'info'));
                }
            } catch (error) {
                container.appendChild(log('Error loading invoices: ' + error.message, 'error'));
            }
        }

        // Customer Testing Functions
        async function createTestCustomer() {
            const container = document.getElementById('customerResults');

            try {
                const testCustomer = {
                    name: `Test Customer ${testCustomerCounter}`,
                    email: `customer${testCustomerCounter}@test.com`,
                    phone: `555-${String(testCustomerCounter).padStart(4, '0')}`,
                    address: `${testCustomerCounter} Test Street`,
                    company: `Test Company ${testCustomerCounter}`,
                    notes: `Test customer #${testCustomerCounter}`
                };

                const result = await window.fileStorageManager.addCustomer(testCustomer);
                container.appendChild(log('Customer created: ' + JSON.stringify(result, null, 2), 'success'));
                testCustomerCounter++;

                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error creating customer: ' + error.message, 'error'));
            }
        }

        async function updateLastCustomer() {
            const container = document.getElementById('customerResults');

            try {
                const customers = await window.fileStorageManager.getActiveCustomers();
                if (customers.length === 0) {
                    container.appendChild(log('No customers to update', 'error'));
                    return;
                }

                const lastCustomer = customers[0];
                const updateData = {
                    name: lastCustomer.name + ' (UPDATED)',
                    phone: '555-UPDATED',
                    notes: lastCustomer.notes + ' (UPDATED)'
                };

                const result = await window.fileStorageManager.updateCustomer(lastCustomer.id, updateData);
                container.appendChild(log('Customer updated: ' + JSON.stringify(result, null, 2), 'success'));

                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error updating customer: ' + error.message, 'error'));
            }
        }

        async function deleteLastCustomer() {
            const container = document.getElementById('customerResults');

            try {
                const customers = await window.fileStorageManager.getActiveCustomers();
                if (customers.length === 0) {
                    container.appendChild(log('No customers to delete', 'error'));
                    return;
                }

                const lastCustomer = customers[0];
                const result = await window.fileStorageManager.softDeleteCustomer(lastCustomer.id);
                container.appendChild(log('Customer soft deleted: ' + JSON.stringify(result, null, 2), 'success'));

                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error deleting customer: ' + error.message, 'error'));
            }
        }

        async function loadActiveCustomers() {
            const container = document.getElementById('customerResults');

            try {
                const customers = await window.fileStorageManager.getActiveCustomers();
                container.appendChild(log(`Loaded ${customers.length} active customers`, 'info'));

                if (customers.length > 0) {
                    container.appendChild(log('Sample: ' + JSON.stringify(customers[0], null, 2), 'info'));
                }
            } catch (error) {
                container.appendChild(log('Error loading customers: ' + error.message, 'error'));
            }
        }

        async function loadSoftDeletedInvoices() {
            const container = document.getElementById('softDeleteResults');

            try {
                const invoices = await window.fileStorageManager.getInactiveInvoices();
                container.appendChild(log(`Found ${invoices.length} soft deleted invoices`, 'info'));

                if (invoices.length > 0) {
                    container.appendChild(log('Sample: ' + JSON.stringify(invoices[0], null, 2), 'info'));
                }
            } catch (error) {
                container.appendChild(log('Error loading soft deleted invoices: ' + error.message, 'error'));
            }
        }

        async function loadSoftDeletedCustomers() {
            const container = document.getElementById('softDeleteResults');

            try {
                const customers = await window.fileStorageManager.getInactiveCustomers();
                container.appendChild(log(`Found ${customers.length} soft deleted customers`, 'info'));

                if (customers.length > 0) {
                    container.appendChild(log('Sample: ' + JSON.stringify(customers[0], null, 2), 'info'));
                }
            } catch (error) {
                container.appendChild(log('Error loading soft deleted customers: ' + error.message, 'error'));
            }
        }

        async function restoreLastDeletedInvoice() {
            const container = document.getElementById('softDeleteResults');

            try {
                const deletedInvoices = await window.fileStorageManager.getInactiveInvoices();
                if (deletedInvoices.length === 0) {
                    container.appendChild(log('No deleted invoices to restore', 'error'));
                    return;
                }

                const lastDeleted = deletedInvoices[0];
                const result = await window.fileStorageManager.restoreInvoice(lastDeleted.id);
                container.appendChild(log('Invoice restored: ' + JSON.stringify(result, null, 2), 'success'));

                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error restoring invoice: ' + error.message, 'error'));
            }
        }

        async function permanentlyDeleteLastInvoice() {
            const container = document.getElementById('softDeleteResults');

            try {
                const deletedInvoices = await window.fileStorageManager.getInactiveInvoices();
                if (deletedInvoices.length === 0) {
                    container.appendChild(log('No deleted invoices to permanently delete', 'error'));
                    return;
                }

                if (!confirm('Permanently delete invoice? This cannot be undone.')) {
                    return;
                }

                const lastDeleted = deletedInvoices[0];
                const result = await window.fileStorageManager.hardDeleteInvoice(lastDeleted.id);
                container.appendChild(log('Invoice permanently deleted: ' + JSON.stringify(result, null, 2), 'success'));

                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error permanently deleting invoice: ' + error.message, 'error'));
            }
        }

        async function restoreLastDeletedCustomer() {
            const container = document.getElementById('softDeleteResults');

            try {
                const deletedCustomers = await window.fileStorageManager.getInactiveCustomers();
                if (deletedCustomers.length === 0) {
                    container.appendChild(log('No deleted customers to restore', 'error'));
                    return;
                }

                const lastDeleted = deletedCustomers[0];
                const result = await window.fileStorageManager.restoreCustomer(lastDeleted.id);
                container.appendChild(log('Customer restored: ' + JSON.stringify(result, null, 2), 'success'));

                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error restoring customer: ' + error.message, 'error'));
            }
        }

        async function permanentlyDeleteLastCustomer() {
            const container = document.getElementById('softDeleteResults');

            try {
                const deletedCustomers = await window.fileStorageManager.getInactiveCustomers();
                if (deletedCustomers.length === 0) {
                    container.appendChild(log('No deleted customers to permanently delete', 'error'));
                    return;
                }

                if (!confirm('Permanently delete customer? This cannot be undone.')) {
                    return;
                }

                const lastDeleted = deletedCustomers[0];
                const result = await window.fileStorageManager.hardDeleteCustomer(lastDeleted.id);
                container.appendChild(log('Customer permanently deleted: ' + JSON.stringify(result, null, 2), 'success'));

                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error permanently deleting customer: ' + error.message, 'error'));
            }
        }

        async function exportData() {
            const container = document.getElementById('dataManagementResults');
            
            try {
                const exportData = await window.fileStorageManager.exportData();
                container.appendChild(log('Data exported: ' + JSON.stringify(exportData, null, 2), 'success'));
            } catch (error) {
                container.appendChild(log('Error exporting data: ' + error.message, 'error'));
            }
        }

        async function createBackup() {
            const container = document.getElementById('dataManagementResults');
            
            try {
                await window.fileStorageManager.createBackup();
                container.appendChild(log('Backup created successfully', 'success'));
            } catch (error) {
                container.appendChild(log('Error creating backup: ' + error.message, 'error'));
            }
        }

        async function clearAllData() {
            const container = document.getElementById('dataManagementResults');
            
            if (!confirm('Clear all data? This will delete everything.')) {
                return;
            }

            try {
                await window.fileStorageManager.clearAllData();
                container.appendChild(log('All data cleared', 'success'));
                testInvoiceCounter = 1;
                await refreshDataDisplay();
            } catch (error) {
                container.appendChild(log('Error clearing data: ' + error.message, 'error'));
            }
        }

        async function refreshDataDisplay() {
            const container = document.getElementById('dataDisplay');
            container.innerHTML = '';

            try {
                const activeInvoices = await window.fileStorageManager.getActiveInvoices();
                const inactiveInvoices = await window.fileStorageManager.getInactiveInvoices();
                const activeCustomers = await window.fileStorageManager.getActiveCustomers();
                const inactiveCustomers = await window.fileStorageManager.getInactiveCustomers();
                const stats = await window.fileStorageManager.getStorageStats();

                // Create summary
                const summary = document.createElement('div');
                summary.innerHTML = `
                    <h4>Data Summary</h4>
                    <h5>📄 Invoices</h5>
                    <p><strong>Active Invoices:</strong> ${activeInvoices.length}</p>
                    <p><strong>Inactive Invoices:</strong> ${inactiveInvoices.length}</p>
                    <p><strong>Total Invoice Records:</strong> ${stats.invoices.totalRecords}</p>
                    <p><strong>Invoice File Size:</strong> ${(stats.invoices.fileSize / 1024).toFixed(2)} KB</p>

                    <h5>👥 Customers</h5>
                    <p><strong>Active Customers:</strong> ${activeCustomers.length}</p>
                    <p><strong>Inactive Customers:</strong> ${inactiveCustomers.length}</p>
                    <p><strong>Total Customer Records:</strong> ${stats.customers.totalRecords}</p>
                    <p><strong>Customer File Size:</strong> ${(stats.customers.fileSize / 1024).toFixed(2)} KB</p>

                    <h5>🔧 System</h5>
                    <p><strong>Total File Size:</strong> ${((stats.invoices.fileSize + stats.customers.fileSize) / 1024).toFixed(2)} KB</p>
                `;
                container.appendChild(summary);

                // Create active invoices table
                if (activeInvoices.length > 0) {
                    const activeTable = createInvoiceTable(activeInvoices, 'Active Invoices');
                    container.appendChild(activeTable);
                }

                // Create inactive invoices table
                if (inactiveInvoices.length > 0) {
                    const inactiveTable = createInvoiceTable(inactiveInvoices, 'Soft Deleted Invoices');
                    container.appendChild(inactiveTable);
                }

                // Create active customers table
                if (activeCustomers.length > 0) {
                    const activeCustomersTable = createCustomerTable(activeCustomers, 'Active Customers');
                    container.appendChild(activeCustomersTable);
                }

                // Create inactive customers table
                if (inactiveCustomers.length > 0) {
                    const inactiveCustomersTable = createCustomerTable(inactiveCustomers, 'Soft Deleted Customers');
                    container.appendChild(inactiveCustomersTable);
                }

            } catch (error) {
                container.appendChild(log('Error refreshing display: ' + error.message, 'error'));
            }
        }

        function createInvoiceTable(invoices, title) {
            const container = document.createElement('div');
            container.innerHTML = `<h4>${title}</h4>`;

            const table = document.createElement('table');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Number</th>
                        <th>Customer</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Active</th>
                        <th>Created</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoices.map(invoice => `
                        <tr>
                            <td>${invoice.number}</td>
                            <td>${invoice.customerName || 'Unknown'}</td>
                            <td>$${(invoice.amount || 0).toLocaleString()}</td>
                            <td>${invoice.status}</td>
                            <td class="${invoice.isActive !== false ? 'status-active' : 'status-inactive'}">
                                ${invoice.isActive !== false ? 'Active' : 'Inactive'}
                            </td>
                            <td>${new Date(invoice.createdAt).toLocaleString()}</td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            container.appendChild(table);
            return container;
        }

        function createCustomerTable(customers, title) {
            const container = document.createElement('div');
            container.innerHTML = `<h4>${title}</h4>`;

            const table = document.createElement('table');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Company</th>
                        <th>Active</th>
                        <th>Created</th>
                    </tr>
                </thead>
                <tbody>
                    ${customers.map(customer => `
                        <tr>
                            <td>${customer.name}</td>
                            <td>${customer.email || 'N/A'}</td>
                            <td>${customer.phone || 'N/A'}</td>
                            <td>${customer.company || 'N/A'}</td>
                            <td class="${customer.isActive !== false ? 'status-active' : 'status-inactive'}">
                                ${customer.isActive !== false ? 'Active' : 'Inactive'}
                            </td>
                            <td>${new Date(customer.createdAt).toLocaleString()}</td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            container.appendChild(table);
            return container;
        }

        // Initialize on page load
        window.addEventListener('load', async function() {
            await initializeSystem();
            await refreshDataDisplay();
        });
    </script>
</body>
</html>
